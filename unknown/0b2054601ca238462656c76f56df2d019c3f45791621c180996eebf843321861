/* eslint-disable @typescript-eslint/no-explicit-any */
import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { Assessment } from 'src/types/models';
import type { DataResponse } from 'src/types/data';
import { AssessmentService } from 'src/services/asm/assessmentService';

export const useEvaluateFormStore = defineStore('evaluateForm', () => {
  // 🔹 State
  const meta = ref<DataResponse<Assessment> | null>(null);
  const assessments = ref<Assessment[]>([]);
  const currentAssessment = ref<Assessment | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  const page = ref(1);
  const limit = ref(5);
  const search = ref('');

  // 🔹 Actions
  const fetchAssessmentById = async (id: number) => {
    loading.value = true;
    error.value = null;
    try {
      const res = await new AssessmentService('evaluate').fetchOne(id);
      if (res) {
        currentAssessment.value = res;
      }
    } catch (err: any) {
      error.value = err?.message ?? 'ไม่สามารถโหลดแบบทดสอบได้';
    } finally {
      loading.value = false;
    }
  };

  const addAssessment = async (assessmentData: Partial<Assessment>): Promise<Assessment> => {
    const res = await new AssessmentService('evaluate').createOne(assessmentData);
    assessments.value.push(res);
    currentAssessment.value = res;
    return res;
  };

  const updateAssessment = async (id: number, assessmentData: Assessment): Promise<Assessment> => {
    const res = await new AssessmentService('evaluate').updateOne(id, assessmentData);
    const index = assessments.value.findIndex((q) => q.id === id);
    if (index !== -1) assessments.value[index] = res;
    if (currentAssessment.value?.id === id) currentAssessment.value = res;
    return res;
  };

  const removeAssessment = async (id: number): Promise<void> => {
    await new AssessmentService('evaluate').deleteOne(id);
    assessments.value = assessments.value.filter((q) => q.id !== id);
    if (currentAssessment.value?.id === id) {
      currentAssessment.value = null;
    }
  };

  // 🔹 ID Tracking Helpers
  const getAssessmentId = (): number | null => {
    return currentAssessment.value?.id || null;
  };

  const getItemBlockById = (id: number) => {
    return currentAssessment.value?.itemBlocks?.find((block) => block.id === id) || null;
  };

  const getHeaderBlockId = (): number | null => {
    const headerBlock = currentAssessment.value?.itemBlocks?.find(
      (block) => block.type === 'HEADER',
    );
    return headerBlock?.id || null;
  };

  const getRadioBlockId = (): number | null => {
    const radioBlock = currentAssessment.value?.itemBlocks?.find((block) => block.type === 'RADIO');
    return radioBlock?.id || null;
  };

  const getAllItemBlockIds = (): number[] => {
    return currentAssessment.value?.itemBlocks?.map((block) => block.id) || [];
  };

  // 🔹 Validation Helpers
  const validateIds = (): { valid: boolean; missing: string[] } => {
    const missing: string[] = [];

    if (!currentAssessment.value?.id) {
      missing.push('assessmentId');
    }

    if (!currentAssessment.value?.itemBlocks || currentAssessment.value.itemBlocks.length === 0) {
      missing.push('itemBlocks');
    } else {
      currentAssessment.value.itemBlocks.forEach((block, index) => {
        if (!block.id) {
          missing.push(`itemBlock[${index}].id`);
        }
        if (!block.assessmentId) {
          missing.push(`itemBlock[${index}].assessmentId`);
        }
      });
    }

    return {
      valid: missing.length === 0,
      missing,
    };
  };

  // 🔹 Enhanced Validation for Block Deletion
  const validateBlockDeletion = (blockId: number): { canDelete: boolean; issues: string[] } => {
    const issues: string[] = [];

    if (!currentAssessment.value) {
      issues.push('No current assessment loaded');
      return { canDelete: false, issues };
    }

    if (!blockId) {
      issues.push('Invalid block ID provided');
      return { canDelete: false, issues };
    }

    const targetBlock = currentAssessment.value.itemBlocks?.find((block) => block.id === blockId);
    if (!targetBlock) {
      issues.push(`Block with ID ${blockId} not found in current assessment`);
      return { canDelete: false, issues };
    }

    // Additional validation for header blocks
    if (targetBlock.type === 'HEADER' && !targetBlock.headerBody) {
      issues.push('Header block missing headerBody data');
    }

    // Check for orphaned references
    if (targetBlock.assessmentId !== currentAssessment.value.id) {
      issues.push(
        `Block assessmentId (${targetBlock.assessmentId}) does not match current assessment ID (${currentAssessment.value.id})`,
      );
    }

    return {
      canDelete: issues.length === 0,
      issues,
    };
  };

  // 🔹 Post-Deletion Validation
  const validatePostDeletion = (deletedBlockId: number): { success: boolean; issues: string[] } => {
    const issues: string[] = [];

    if (!currentAssessment.value) {
      issues.push('No current assessment loaded');
      return { success: false, issues };
    }

    // Check if the block still exists in the assessment
    const blockStillExists = currentAssessment.value.itemBlocks?.some(
      (block) => block.id === deletedBlockId,
    );
    if (blockStillExists) {
      issues.push(`Block with ID ${deletedBlockId} still exists in assessment after deletion`);
    }

    // Check for any orphaned references
    const orphanedQuestions = currentAssessment.value.itemBlocks?.some((block) =>
      block.questions?.some((question) => question.itemBlockId === deletedBlockId),
    );
    if (orphanedQuestions) {
      issues.push(`Found orphaned questions referencing deleted block ID ${deletedBlockId}`);
    }

    const orphanedOptions = currentAssessment.value.itemBlocks?.some((block) =>
      block.options?.some((option) => option.itemBlockId === deletedBlockId),
    );
    if (orphanedOptions) {
      issues.push(`Found orphaned options referencing deleted block ID ${deletedBlockId}`);
    }

    return {
      success: issues.length === 0,
      issues,
    };
  };

  return {
    // state
    assessments,
    currentAssessment,
    loading,
    error,
    meta,
    page,
    limit,
    search,
    // actions
    fetchAssessmentById,
    addAssessment,
    updateAssessment,
    removeAssessment,
    // ID tracking helpers
    getAssessmentId,
    getItemBlockById,
    getHeaderBlockId,
    getRadioBlockId,
    getAllItemBlockIds,
    validateIds,
    // Enhanced validation helpers
    validateBlockDeletion,
    validatePostDeletion,
  };
});
