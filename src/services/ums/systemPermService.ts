import type SystemPerm from 'src/types/ums/systemPerm';
import { api } from 'src/boot/axios';
import { Notify } from 'quasar';

interface GetAllSystemPermsParams {
  sortBy: unknown;
  search: { permName: string };
  page: number;
  itemsPerPage: number;
}

const showError = (message: string) => {
  Notify.create({
    message,
    type: 'negative',
    position: 'bottom',
    timeout: 3000,
  });
};

const getOneSystemPerm = async (perId: number) => {
  try {
    return await api.get(`ums-permission/${perId}`);
  } catch {
    showError('ไม่สามารถโหลดสิทธิ์ระบบได้');
    throw new Error('Get one system permission failed');
  }
};

const getSystemPermActives = async () => {
  try {
    return await api.get(`ums-permission/actives`);
  } catch {
    showError('ไม่สามารถโหลดสิทธิ์ที่เปิดใช้งานได้');
    throw new Error('Get active system permissions failed');
  }
};

const getAllSystemPerms = async (params: GetAllSystemPermsParams) => {
  try {
    return await api.get(`ums-permission/perms`, { params });
  } catch {
    showError('ไม่สามารถโหลดสิทธิ์ทั้งหมดได้');
    throw new Error('Get all system permissions failed');
  }
};

const addSystemPerm = async (data: SystemPerm) => {
  try {
    return await api.post(`ums-permission`, data);
  } catch {
    showError('ไม่สามารถเพิ่มสิทธิ์ระบบได้');
    throw new Error('Add system permission failed');
  }
};

const editSystemPerm = async (systemPerm: SystemPerm) => {
  try {
    return await api.patch(`ums-permission/${systemPerm.perId}`, systemPerm);
  } catch {
    showError('ไม่สามารถแก้ไขสิทธิ์ระบบได้');
    throw new Error('Edit system permission failed');
  }
};

const deleteSystemPerm = async (perId: number) => {
  try {
    return await api.delete(`ums-permission/${perId}`);
  } catch {
    showError('ไม่สามารถลบสิทธิ์ระบบได้');
    throw new Error('Delete system permission failed');
  }
};

export default {
  getOneSystemPerm,
  getSystemPermActives,
  getAllSystemPerms,
  addSystemPerm,
  editSystemPerm,
  deleteSystemPerm,
};
