// ! need new implementation
// import http from '../axiosConfig';
// import type {
//   QuizSummaryDto,
//   UserAttemptsQueryParams,
//   PaginatedUserAttemptsResponse,
//   QuizAnswerSummaryResponseDto,
// } from 'src/types/quiz/quizDashBoardTypes.ts';

// const BASE_PATH = '/quiz-dashboard';
// async function getQuizSummary(quizId: number): Promise<QuizSummaryDto> {
//   try {
//     const response = await http.get<QuizSummaryDto>(`${BASE_PATH}/${quizId}/summary`);
//     return response.data;
//   } catch (error) {
//     console.error(`Error fetching quiz summary for quiz ID ${quizId}:`, error);
//     throw error;
//   }
// }
// async function getUserAttempts(
//   quizId: number,
//   params: UserAttemptsQueryParams = {}
// ): Promise<PaginatedUserAttemptsResponse> {
//   try {
//     const response = await http.get<PaginatedUserAttemptsResponse>(
//       `${BASE_PATH}/${quizId}/user-attempts`,
//       { params: params }
//     );
//     return response.data;
//   } catch (error) {
//     console.error(`Error fetching user attempts for quiz ID ${quizId}:`, error);
//     throw error;
//   }
// }

// async function getQuizAnswerSummary(quizId: number): Promise<QuizAnswerSummaryResponseDto> {
//   try {
//     const response = await http.get<QuizAnswerSummaryResponseDto>(
//       `${BASE_PATH}/${quizId}/answer-summary`
//     );
//     return response.data;
//   } catch (error) {
//     console.error(`Error fetching quiz answer summary for quiz ID ${quizId}:`, error);
//     throw error;
//   }
// }

// export default {
//   getQuizSummary,
//   getUserAttempts,
//   getQuizAnswerSummary,
// };
