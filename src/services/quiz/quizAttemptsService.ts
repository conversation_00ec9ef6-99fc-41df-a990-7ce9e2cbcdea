// import axios from '../axiosConfig';

// const startQuizAttempt = async (userId: number, quizId: number): Promise<{ data: QuizAttempt }> => {
//   const body = { userId, quizId };
//   console.log(body);
//   const data = await axios.post('/quiz-attempts/start', body);
//   console.log(data);

//   return data;
// };

// const submitQuizAttempt = (id: number): Promise<{ data: Submit }> => {
//   return axios.post(`/quiz-attempts/${id}/submit`);
// };

// const getAllQuizAttempts = (
//   params?: Record<string, string | number | boolean>,
// ): Promise<{ data: QuizAttempt[] }> => {
//   return axios.get('/quiz-attempts', { params });
// };

// const getActiveQuizAttempt = (): Promise<{ data: QuizAttempt | null }> => {
//   return axios.get('/quiz-attempts/active');
// };

// const getQuizAttemptById = (id: number): Promise<{ data: QuizAttempt }> => {
//   return axios.get(`/quiz-attempts/${id}`);
// };

// const saveAnswer = (id: number, quizAttemptAnswer: AttemptAnswer) => {
//   return axios.post(`/quiz-attempts/${id}/save`, quizAttemptAnswer);
// };

// const navigateToQuestion = (
//   id: number,
//   sequence: number,
// ): Promise<{ data: NavigateQuestionResponse }> => {
//   return axios.post(`/quiz-attempts/${id}/${sequence}/navigate`);
// };

// const getQuestionBySequence = (
//   id: number,
//   questionSequence: number,
// ): Promise<{ data: NavigateQuestionResponse }> => {
//   return axios.get(`/quiz-attempts/${id}/questions/${questionSequence}`);
// };

// export default {
//   startQuizAttempt,
//   submitQuizAttempt,
//   getAllQuizAttempts,
//   getActiveQuizAttempt,
//   getQuizAttemptById,
//   saveAnswer,
//   navigateToQuestion,
//   getQuestionBySequence,
// };
