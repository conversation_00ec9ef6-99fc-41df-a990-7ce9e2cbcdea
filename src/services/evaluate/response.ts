import type { DataParams } from 'src/types/data';
import { api } from 'src/boot/axios';
import type { Response } from 'src/types/models';

const getAll = (params: DataParams, programId: number): Promise<{ data: Response[] }> => {
  return api.post('http://localhost:3000/evaluate/responses/', {
    ...params,
    programId,
  });
};

const getOne = (id: number, section: number): Promise<{ data: Response }> => {
  return api.get(`http://localhost:3000/evaluate/responses/${id}`, {
    params: { section },
  });
};

const createResponse = async (data: Partial<Response>): Promise<Response | null> => {
  try {
    const res = await api.post('http://localhost:3000/evaluate/responses/', data);
    return res.data;
  } catch (error) {
    console.error('Failed to create response:', error);
    return null;
  }
};
const updateResponse = async (id: number, data: Partial<Response>): Promise<Response | null> => {
  try {
    const res = await api.patch(`http://localhost:3000/evaluate/responses/${id}`, data);
    return res.data;
  } catch (error) {
    console.error('Failed to update response:', error);
    return null;
  }
};
const deleteResponse = (data: {
  submissionId: number;
  questionId: number;
  selectedOptionId: number;
}) => {
  return api.delete('http://localhost:3000/evaluate/responses', { data });
};

export default {
  getAll,
  getOne,
  createResponse,
  updateResponse,
  deleteResponse,
};
