import { api } from 'src/boot/axios';
import { Notify } from 'quasar';
import type { Option } from 'src/types/models';

export class OptionService {
  private path = '/options';

  async createOption(data: Option, file?: File): Promise<Option> {
    try {
      const formData = this.toFormData(data, file);
      const response = await api.post<Option>(this.path, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      Notify.create({ type: 'positive', message: 'สร้างตัวเลือกสำเร็จ' });
      return response.data;
    } catch {
      Notify.create({ type: 'negative', message: 'สร้างตัวเลือกล้มเหลว' });
      throw new Error('Create option failed');
    }
  }

  async getAllOptions(): Promise<Option[]> {
    try {
      const response = await api.get<Option[]>(this.path);
      return response.data;
    } catch {
      throw new Error('Fetch options failed');
    }
  }

  async getOptionById(id: number): Promise<Option> {
    try {
      const response = await api.get<Option>(`${this.path}/${id}`);
      return response.data;
    } catch {
      throw new Error('Fetch option failed');
    }
  }

  async updateOption(id: number, data: Option, file?: File): Promise<Option> {
    try {
      const formData = this.toFormData(data, file);
      const response = await api.put<Option>(`${this.path}/${id}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      Notify.create({ type: 'positive', message: 'อัปเดตตัวเลือกสำเร็จ' });
      return response.data;
    } catch {
      Notify.create({ type: 'negative', message: 'อัปเดตตัวเลือกล้มเหลว' });
      throw new Error('Update option failed');
    }
  }

  async removeOption(id: number): Promise<void> {
    try {
      await api.delete(`${this.path}/${id}`);
      Notify.create({ type: 'positive', message: 'ลบตัวเลือกสำเร็จ' });
    } catch {
      Notify.create({ type: 'negative', message: 'ลบตัวเลือกล้มเหลว' });
      throw new Error('Remove option failed');
    }
  }
  private toFormData(data: Record<string, unknown>, file?: File): FormData {
    const formData = new FormData();

    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object' && value !== null && !(value instanceof File)) {
          formData.append(key, JSON.stringify(value));
        } else if (
          typeof value === 'string' ||
          typeof value === 'number' ||
          typeof value === 'boolean'
        ) {
          formData.append(key, String(value));
        } else {
          formData.append(key, JSON.stringify(value));
        }
      }
    });

    if (file) {
      formData.append('file', file);
    }

    return formData;
  }
}
