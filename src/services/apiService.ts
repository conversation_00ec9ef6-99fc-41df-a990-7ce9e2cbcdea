import { api as http } from 'src/boot/axios';
import { Notify } from 'quasar';

const showError = (message: string) => {
  Notify.create({
    message,
    type: 'negative',
    position: 'bottom',
    timeout: 3000,
  });
};

async function uploadFile(filePath: string, fileName: string, fileType: string, files: File[]) {
  const formData = new FormData();
  formData.append('path', filePath);
  formData.append('fileName', fileName);
  formData.append('fileType', fileType);
  files.forEach((file) => {
    formData.append('files[]', file);
  });

  try {
    return await http.post('/api/uploadFile', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  } catch {
    showError('ไม่สามารถอัปโหลดไฟล์ได้');
    throw new Error('Upload failed');
  }
}

async function getFileList(path: string) {
  try {
    return await http.post('/api/getFileList', { path });
  } catch {
    showError('ไม่สามารถดึงรายการไฟล์ได้');
    throw new Error('Get file list failed');
  }
}

async function getPublicFile(fileName: string) {
  try {
    return await http.post('/api/getPublicFile', { fileName });
  } catch {
    showError('ไม่สามารถดึงไฟล์สาธารณะได้');
    throw new Error('Get public file failed');
  }
}

async function getPublicFiles(arg: { files: { fileName: string }[] }) {
  try {
    return await http.post('/api/getPublicFiles', arg);
  } catch {
    showError('ไม่สามารถดึงไฟล์หลายรายการได้');
    throw new Error('Get public files failed');
  }
}

async function deleteFile(fileName: string) {
  try {
    const res = await http.delete('/api/deleteFile', { data: { fileName } });
    Notify.create({ message: 'ลบเรียบร้อยแล้ว', type: 'positive' });
    return res;
  } catch {
    showError('ไม่สามารถลบไฟล์ได้');
    throw new Error('Delete file failed');
  }
}

async function dsPrefix() {
  try {
    return await http.get('/api/dsPrefix');
  } catch {
    showError('ไม่สามารถโหลด prefix ได้');
    throw new Error('Get prefix failed');
  }
}

export default {
  uploadFile,
  getFileList,
  getPublicFile,
  getPublicFiles,
  deleteFile,
  dsPrefix,
};
