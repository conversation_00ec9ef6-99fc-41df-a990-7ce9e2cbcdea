import type { ItemBlock } from 'src/types/models';

// Mockup ItemBlock for all ItemBlockType
export const mockItemBlocks: ItemBlock[] = [
  // HEADER
  {
    id: 1,
    assessmentId: 1,
    sequence: 1,
    section: 1,
    type: 'HEADER',
    isRequired: false,
    headerBody: {
      id: 1,
      itemBlockId: 1,
      title: 'หัวข้อแบบทดสอบ',
      description: 'รายละเอียดหัวข้อแบบทดสอบ',
    },
  },
  // IMAGE
  {
    id: 2,
    assessmentId: 1,
    sequence: 2,
    section: 1,
    type: 'IMAGE',
    isRequired: false,
    imageBody: {
      id: 2,
      itemBlockId: 2,
      imagePath: '/mockup/avatar.webp',
      imageText: 'คำอธิบายรูปภาพ',
    },
  },
  // RADIO (single choice)
  {
    id: 3,
    assessmentId: 1,
    sequence: 3,
    section: 1,
    type: 'RADIO',
    isRequired: true,
    questions: [
      {
        id: 3,
        itemBlockId: 3,
        questionText: 'ตัวอย่างคำถามแบบตัวเลือกเดียว',
        isHeader: false,
        sequence: 1,
        score: 1,
      },
    ],
    options: [
      { id: 1, itemBlockId: 3, optionText: 'ตัวเลือก 1', value: 0, sequence: 1 },
      { id: 2, itemBlockId: 3, optionText: 'ตัวเลือก 2', value: 1, sequence: 2 },
    ],
  },
  // CHECKBOX (multiple choice)
  {
    id: 4,
    assessmentId: 1,
    sequence: 4,
    section: 1,
    type: 'CHECKBOX',
    isRequired: true,
    questions: [
      {
        id: 4,
        itemBlockId: 4,
        questionText: 'ตัวอย่างคำถามแบบหลายตัวเลือก',
        isHeader: false,
        sequence: 1,
        score: 1,
      },
    ],
    options: [
      { id: 3, itemBlockId: 4, optionText: 'ตัวเลือก ก', value: 0, sequence: 1 },
      { id: 4, itemBlockId: 4, optionText: 'ตัวเลือก ข', value: 1, sequence: 2 },
    ],
  },
  // TEXTFIELD (short answer)
  {
    id: 5,
    assessmentId: 1,
    sequence: 5,
    section: 1,
    type: 'TEXTFIELD',
    isRequired: true,
    questions: [
      {
        id: 5,
        itemBlockId: 5,
        questionText: 'ตัวอย่างคำถามแบบเขียน',
        isHeader: false,
        sequence: 1,
        score: 0,
      },
    ],
  },
  // GRID (multiple choice grid)
  {
    id: 6,
    assessmentId: 1,
    sequence: 6,
    section: 1,
    type: 'GRID',
    isRequired: true,
    questions: [
      {
        id: 6,
        itemBlockId: 6,
        questionText: 'ตัวอย่างคำถามแบบกริด',
        isHeader: true,
        sequence: 0,
        score: 0,
      },
      {
        id: 7,
        itemBlockId: 6,
        questionText: 'แถวที่ 1',
        isHeader: false,
        sequence: 1,
        score: 0,
      },
      {
        id: 8,
        itemBlockId: 6,
        questionText: 'แถวที่ 2',
        isHeader: false,
        sequence: 2,
        score: 0,
      },
    ],
    options: [
      { id: 5, itemBlockId: 6, optionText: 'คอลัมน์ 1', value: 0, sequence: 1 },
      { id: 6, itemBlockId: 6, optionText: 'คอลัมน์ 2', value: 0, sequence: 2 },
    ],
  },
  // FILE (file upload)
  {
    id: 7,
    assessmentId: 1,
    sequence: 7,
    section: 1,
    type: 'FILE',
    isRequired: false,
    questions: [
      {
        id: 9,
        itemBlockId: 7,
        questionText: 'อัปโหลดไฟล์',
        isHeader: false,
        sequence: 1,
        score: 0,
        sizeLimit: 5, // MB
        acceptFile: '.pdf,.docx,.jpg',
        uploadLimit: 1,
      },
    ],
  },
];
