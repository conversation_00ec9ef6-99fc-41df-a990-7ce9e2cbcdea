<template>
  <q-card class="q-pa-md q-ma-md evaluate-item" @click="$emit('focus-fab')">
    <!-- หัวข้อ -->
    <div class="row items-center q-mb-md q-pt-md q-ml-md">
      <div class="col">
        <q-input
          v-model="questionText"
          placeholder="พิมพ์คำถาม..."
          class="q-mr-sm"
          style="max-width: 400px; font-size: 20px"
          @update:model-value="updateQuestion"
        />
      </div>
      <div class="col-1">
        <q-btn flat round icon="image" color="grey" />
      </div>
      <!-- dropdown menu -->
      <div class="col-auto">
        <q-select
          v-model="selectedType"
          :options="typeOptions"
          filled
          dense
          style="min-width: 200px"
          option-label="label"
          option-value="value"
          @update:model-value="onTypeChange"
          prepend-icon="list"
        >
          <template v-slot:option="scope">
            <q-item v-bind="scope.itemProps">
              <q-item-section avatar>
                <q-icon :name="scope.opt.icon" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ scope.opt.label }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
          <template v-slot:prepend>
            <q-icon v-if="selectedType" :name="selectedType.icon" class="q-mr-sm" />
          </template>
        </q-select>
      </div>
    </div>
    <!-- คำถาม -->
    <component :is="currentComponent" class="q-mb-md q-pt-md q-ml-md" />
    <!-- ปุ่มควบคุม -->
    <q-separator inset color="#898989" />
    <div class="row items-center justify-end q-mt-md">
      <q-btn flat round icon="open_in_new" color="grey" />
      <ItemCardFooter label="จำเป็น" @delete="$emit('delete')" />
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import ChoiceAns from 'src/components/evaluate/EvaluateBlock/QuestionType/ChoiceAns.vue';
import TextAns from 'src/components/evaluate/EvaluateBlock/QuestionType/TextAns.vue';
// import GridAns from 'src/components/evaluate/EvaluateBlock/QuestionType/GridAns.vue'; // GridAns is not in QuestionType, commenting out for now
import CheckBoxAns from 'src/components/evaluate/EvaluateBlock/QuestionType/CheckBoxAns.vue';
import ItemCardFooter from 'src/components/common/ItemCardFooter.vue';
import type { ItemType, Option } from 'src/types/models';

const questionText = ref<string>('');

// select type of question
interface TypeOption {
  label: string;
  value: ItemType;
  icon: string;
}
const typeOptions: TypeOption[] = [
  { label: 'ปรนัย (เลือกตอบข้อเดียว)', value: 'RADIO', icon: 'radio_button_checked' },
  { label: 'ปรนัย (เลือกตอบหลายข้อ)', value: 'CHECKBOX', icon: 'check_box' },
  { label: 'อัตนัย (คำตอบสั้นๆ)', value: 'TEXTFIELD', icon: 'short_text' },
];
const selectedType = ref<TypeOption>(typeOptions[0]!); // Default to SINGLE choice

// props สำหรับรับข้อมูลจากคอมโพเนนต์แม่
const props = defineProps<{
  sequence: number;
}>();

// emit events สำหรับส่งข้อมูลกลับไปยังคอมโพเนนต์แม่
const emit = defineEmits<{
  (
    e: 'update-question',
    questionData: {
      id?: string;
      question: string;
      type: ItemType;
      points: number;
      choices: Option[];
      sequence: number;
      quizId: number;
      answer: string;
    },
  ): void;
  (e: 'delete-question', id?: string): void;
  (e: 'focus-fab'): void;
  (e: 'delete'): void;
}>();

// ฟังก์ชันสำหรับอัปเดตคำถามใน store
const updateQuestion = () => {
  if (quizStore.currentQuiz) {
    const questionData = {
      question: questionText.value,
      type: selectedType.value.value,
      points: 1, // default value TODO: if has select points add logic here
      choices: [], // จะถูกเติมข้อมูลจาก component ลูก
      sequence: props.sequence, // ลำดับคำถามอัตโนมัติ
      quizId: quizStore.currentQuiz.id, // รหัสของแบบทดสอบ - แปลงเป็น string
      answer: '', // คำตอบเริ่มต้น (สำหรับคำถามแบบอัตนัย)
    };
    emit('update-question', questionData);
  }
};

const onTypeChange = (value: TypeOption) => {
  console.log('Selected type:', value);
  updateQuestion();
};

// โหลดข้อมูลจาก store เมื่อ component ถูกสร้าง
onMounted(() => {
  if (quizStore.currentQuiz) {
    // ถ้ามีข้อมูลใน store ให้ใช้ข้อมูลนั้น
    console.log('Quiz data from store:', quizStore.currentQuiz);
    // ตรงนี้สามารถเพิ่มโค้ดเพื่อดึงข้อมูลจาก store มาใช้ได้
    // ตัวอย่าง: ถ้ามีคำถามใน store ให้แสดงคำถามนั้น
    if (quizStore.currentQuiz.questions && quizStore.currentQuiz.questions.length > 0) {
      const firstQuestion = quizStore.currentQuiz.questions[0];
      if (firstQuestion) {
        questionText.value = firstQuestion.question;

        // ค้นหาประเภทคำถามจาก enum value
        const foundType = typeOptions.find((opt) => opt.value === firstQuestion.type);
        if (foundType) {
          selectedType.value = foundType;
        }
      }
    }
  }
});

// กำหนดComponentที่จะแสดงตามประเภทคำถาม
const currentComponent = computed(() => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const components: Record<ItemType, any> = {
    RADIO: ChoiceAns,
    CHECKBOX: CheckBoxAns,
    TEXTFIELD: TextAns,
    GRID: undefined, // Placeholder, update with actual component if available
    HEADER: undefined, // Placeholder, update with actual component if available
    IMAGE: undefined, // Placeholder, update with actual component if available
  };
  return components[selectedType.value.value] || ChoiceAns; // selectedType.value.value is correct as it refers to QuestionType enum value
});
</script>

<style scoped>
.q-select {
  box-sizing: border-box;
  width: 255px;
  background: #fffdfd;
  border: 1px solid #b1b1b1;
  border-radius: 10px;
}
</style>
