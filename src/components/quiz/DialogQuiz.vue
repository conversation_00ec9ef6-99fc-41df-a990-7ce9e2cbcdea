<template>
  <q-dialog v-model="show">
    <q-card style="min-width: 300px; max-width: 600px;">


      <div class="flex flex-center q-my-md full-width">
        <q-icon name="schedule_off" size="120px" class="text-negative" style="display: block;" />
      </div>
      <q-card-section class="text-center">
        <p color="negative">"หมดเวลาแล้ว"</p>เวลาทำแบบทดสอบหมดลง กรุณากด "ตกลง" เพื่อดำเนินการต่อ
      </q-card-section>

      <q-card-actions align="center" style="margin-top:10px ;margin-bottom: 10px;">
        <q-btn label="ตกลง" color="primary" @click="confirm" style="width: 150px;" />
      </q-card-actions>

    </q-card>
  </q-dialog>
</template>


<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm'): void
}>()

const show = ref(props.modelValue)

watch(() => props.modelValue, val => show.value = val)
watch(show, val => emit('update:modelValue', val))

function confirm() {
  emit('confirm')
  show.value = false
}
</script>
