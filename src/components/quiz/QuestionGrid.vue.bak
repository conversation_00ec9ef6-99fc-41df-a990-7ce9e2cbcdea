<template>
  <q-btn round color="primary" icon="apps" class="q-pa-sm" size="lg">
    <q-menu ref="menuRef" fit anchor="bottom right" self="top right" :offset="[0, 10]" persistent>
      <q-card class="float-button">
        <q-card-section class="q-gutter-sm row justify-start items-center">
          <div
            v-for="num in questionsOnPage"
            :key="num"
            @click="select(num)"
            :class="buttonClass(num)"
            class="q-pa-sm rounded-borders cursor-pointer text-center"
            style="width: 35px; height: 38px"
          >
            {{ num }}
          </div>
        </q-card-section>

        <q-card-actions align="center">
          <q-btn flat icon="chevron_left" :disable="currentPage === 1" @click="prevPage" />
          <span>หน้า {{ currentPage }}</span>
          <q-btn flat icon="chevron_right" :disable="endIndex >= total" @click="nextPage" />
        </q-card-actions>
      </q-card>
    </q-menu>
  </q-btn>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

const props = defineProps<{
  total: number;
  answered?: number[];
  modelValue: number;
}>();

const emit = defineEmits(['update:modelValue']);

const menuRef = ref();
const perPage = 20;
const currentPage = ref(1);

const answeredList = computed(() => props.answered ?? []);

const startIndex = computed(() => (currentPage.value - 1) * perPage + 1);
const endIndex = computed(() => Math.min(startIndex.value + perPage - 1, props.total));

const questionsOnPage = computed(() => {
  const arr = [];
  for (let i = startIndex.value; i <= endIndex.value; i++) {
    arr.push(i);
  }
  return arr;
});

function select(num: number) {
  emit('update:modelValue', num);
  menuRef.value?.hide();
}

function buttonClass(num: number) {
  if (num === props.modelValue) return 'bg-grey-5';
  if (answeredList.value.includes(num)) return 'bg-green-6 text-white';
  return 'bg-white text-dark shadow-2';
}

function prevPage() {
  if (currentPage.value > 1) currentPage.value--;
}

function nextPage() {
  if (endIndex.value < props.total) currentPage.value++;
}
</script>

<style scoped>
.rounded-borders {
  border-radius: 12px;
}
.float-button {
  width: 248px;
  background: #f0f0f0;
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.25);
  border-radius: 21px;
}
</style>
