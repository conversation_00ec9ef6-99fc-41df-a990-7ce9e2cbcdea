<template>
  <div class="header-bar bg-secondary q-px-md row items-center justify-between" style="border: 0">
    <!-- Left: Title and Status -->
    <div class="col-grow">
      <div class="row item-center">
        <div class="text-white header q-my-auto">การจัดการแบบทดสอบ</div>
        <div class="q-ml-md text-white sub-body q-my-auto">กำลังบันทึก...</div>
      </div>
    </div>

    <!-- Right: Icons + Button -->
    <div class="row items-center q-gutter-sm no-wrap">
      <q-btn flat round icon="link" size="lg" class="text-white" @click="onLink" />
      <q-btn flat round icon="visibility" size="lg" class="text-white" @click="onPreview" />
      <q-btn
        unelevated
        label="เผยแพร่"
        color="primary"
        @click="onPublish"
        class="btn sub-body flex items-center justify-center text-black publish-btn"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();

const onLink = () => {
  console.log('Link clicked');
};

const onPreview = async () => {
  await router.push('/quiz/management/create-quiz');
  console.log('Preview clicked');
};

const onPublish = () => {
  console.log('Publish clicked');
};
</script>

<style scoped lang="scss">
.header-bar {
  min-height: 45px;
}

.publish-btn {
  height: 45px;
  width: 140px;
  color: black;
  font-weight: 500;
  padding: 4px 16px;
  border-radius: 12px;
  font-size: 0.85rem;
}

@media (max-width: 600px) {
  .header-bar {
    flex-direction: column;
    align-items: flex-start !important;
  }
}
</style>
