<template>
  <q-page>
    <!-- Header -->
    <div
      class="bg-primary text-white q-ps-md q-pr-md q-pl-md flex items-center justify-between rounded-b-lg"
    >
      <div class="text-subtitle1">
        {{ props.title }}
        <span class="text-caption text-grey-4">{{ props.subtitle }}</span>
      </div>
      <div class="row items-center q-gutter-sm">
        <q-btn round dense icon="link" flat color="grey-5" />
        <q-btn round dense icon="visibility" flat color="grey-5" />
        <q-btn color="deep-purple-4" label="เผยแพร่" no-caps unelevated />
      </div>
    </div>

    <!-- Tab Menu -->
    <q-tabs
      v-model="tab"
      class="custom-tab bg-white text-white"
      align="center"
      narrow-indicator
      dense
    >
      <q-tab name="question" label="คำถาม" />
      <q-tab name="answer" label="การตอบกลับ" />
      <q-tab name="setting" label="ตั้งค่า" />
    </q-tabs>

    <!-- Tab Panel Content -->
    <div class="q-pa-md">
      <q-tab-panels v-model="tab" animated>
        <q-tab-panel name="question">
          <slot name="question" />
        </q-tab-panel>
        <q-tab-panel name="answer">
          <slot name="answer" />
        </q-tab-panel>
        <q-tab-panel name="setting">
          <slot name="setting" />
        </q-tab-panel>
      </q-tab-panels>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, defineProps } from 'vue';

const props = defineProps<{
  title?: string;
  subtitle?: string;
}>();

const tab = ref<'question' | 'answer' | 'setting'>('question');
</script>

<style scoped>
::v-deep(.custom-tab .q-tab) {
  font-size: 13px;
  padding: 4px 12px;
  min-height: 36px;
}

::v-deep(.custom-tab .q-tab--active) {
  background-color: white;
  color: var(--q-secondary);
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

::v-deep(.q-page) {
  padding-left: 0 !important;
  margin: 0 !important;
  width: 100%;
}
</style>
