<template>
  <q-dialog v-model="isOpen" persistent>
    <q-card>
      <q-card-section class="row items-center q-pb-none">
        <q-icon name="help_outline" size="32px" color="primary" class="q-mr-sm" />
        <div class="text-h6">{{ title }}</div>
      </q-card-section>

      <q-card-section class="q-pt-none">
        <slot>
          <div>คุณแน่ใจหรือไม่ ที่จะดำเนินการนี้ ?</div>
        </slot>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat label="ยกเลิก" @click="cancel" style="background-color: red" />
        <q-btn flat label="ยืนยัน" @click="confirm" class="q-bg-" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, watch } from 'vue';

const props = defineProps<{
  modelValue: boolean;
  title: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
  (e: 'confirm'): void;
  (e: 'cancel'): void;
}>();

const isOpen = ref(props.modelValue);

watch(
  () => props.modelValue,
  (val) => {
    isOpen.value = val;
  },
);

watch(isOpen, (val) => {
  emit('update:modelValue', val);
});

const confirm = () => {
  emit('confirm');
  isOpen.value = false;
};

const cancel = () => {
  emit('cancel');
  isOpen.value = false;
};
</script>
<style scoped></style>
