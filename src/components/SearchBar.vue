<template>
  <q-input
    style="width: 325px"
    outlined
    dense
    debounce="300"
    v-model="searchText"
    placeholder="ค้นหา"
    class="q-mr-sm"
  >
    <template #prepend>
      <q-icon name="search" />
    </template>
  </q-input>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

const emit = defineEmits<{
  (e: 'search', value: string): void;
}>();

const searchText = ref('');

watch(searchText, (val) => {
  emit('search', val);
});
</script>
