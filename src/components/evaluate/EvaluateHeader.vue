<template>
  <q-card style="display: flex; justify-content: left" @click="$emit('focus-fab')" class="q-mb-md">
    <div class="evaluate-item">
      <div class="input-wrapper">
        <div
          ref="edtiableHeader"
          class="editable-div"
          contenteditable
          @input="updateInputHeader"
          @click="showHeadTool = true"
          @focus="isFocused = true"
          @blur="isFocused = false"
          data-placeholder="หัวข้อ"
        ></div>
        <q-separator></q-separator>
      </div>
      <q-slide-transition style="background-color: none" class="slideTool">
        <div v-show="showHeadTool" style="background-color: none" ref="headToolbar">
          <q-toolbar v-show="showHeadTool" class="bg-white-2 text-black rounded-borderstoolbar">
            <q-btn flat round icon="format_bold" @click="exec('bold')" />
            <q-btn flat round icon="format_italic" @click="exec('italic')" />
            <q-btn flat round icon="format_underline" @click="exec('underline')" />
            <q-btn flat round icon="link" @click="insertLink" />
            <q-btn flat round icon="format_clear" @click="exec('removeFormat')" />
          </q-toolbar>
        </div>
      </q-slide-transition>

      <!--Decsription-->
      <div class="input-wrapper">
        <div
          ref="editableDescrip"
          class="editable-div"
          contenteditable
          @input="updateInputDescrip"
          @click="showDescripTool = true"
          @focus="isDescripFocused = true"
          @blur="isDescripFocused = false"
          data-placeholder="คำอธิบาย"
        ></div>

        <q-separator></q-separator>
      </div>
      <q-slide-transition style="background-color: none" class="slideTool">
        <div v-show="showDescripTool" style="background-color: none" ref="descriptoolbar">
          <q-toolbar v-show="showDescripTool" class="bg-white-2 text-black rounded-borders toolbar">
            <q-btn flat round icon="format_bold" @click="exec('bold')" />
            <q-btn flat round icon="format_italic" @click="exec('italic')" />
            <q-btn flat round icon="format_underline" @click="exec('underline')" />
            <q-btn flat round icon="link" @click="insertLink" />
            <q-btn flat round icon="format_list_bulleted" @click="exec('insertUnorderedList')" />
            <q-btn flat round icon="format_list_numbered" @click="exec('insertOrderedList')" />
            <q-btn flat round icon="format_clear" @click="exec('removeFormat')" />
          </q-toolbar>
        </div>
      </q-slide-transition>
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref, watch } from 'vue';

const props = defineProps({
  initialHeader: {
    type: String,
    default: '',
  },
  initialDescription: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['focus-fab', 'update:header', 'update:description']);

const formattedHeader = ref(props.initialHeader || '');
const headToolbar = ref<HTMLElement | null>(null);
const showHeadTool = ref(false);
const isFocused = ref(false);
const inputRef = ref<HTMLElement | null>(null);
const edtiableHeader = ref<HTMLElement | null>(null);
const headText = ref(false);

const formattedDescrip = ref(props.initialDescription || '');
const descriptoolbar = ref<HTMLElement | null>(null);
const showDescripTool = ref(false);
const isDescripFocused = ref(false);
const inputDescripRef = ref<HTMLElement | null>(null);
const editableDescrip = ref<HTMLElement | null>(null);
const DescripText = ref(false);

// Initialize content from props
onMounted(() => {
  if (edtiableHeader.value && props.initialHeader) {
    edtiableHeader.value.innerHTML = props.initialHeader;
    headText.value = props.initialHeader.trim() !== '';
  }

  if (editableDescrip.value && props.initialDescription) {
    editableDescrip.value.innerHTML = props.initialDescription;
    DescripText.value = props.initialDescription.trim() !== '';
  }
});

// Watch for prop changes
watch(
  () => props.initialHeader,
  (newValue) => {
    if (edtiableHeader.value && newValue && edtiableHeader.value.innerHTML !== newValue) {
      edtiableHeader.value.innerHTML = newValue;
      headText.value = newValue.trim() !== '';
    }
  },
);

watch(
  () => props.initialDescription,
  (newValue) => {
    if (editableDescrip.value && newValue && editableDescrip.value.innerHTML !== newValue) {
      editableDescrip.value.innerHTML = newValue;
      DescripText.value = newValue.trim() !== '';
    }
  },
);

function exec(command: string) {
  document.execCommand(command);
}

function insertLink() {
  const url = prompt('กรอก URL');
  if (url) {
    document.execCommand('createLink', false, url);
  }
}

function updateInputHeader() {
  if (edtiableHeader.value) {
    formattedHeader.value = edtiableHeader.value.innerHTML;
    const content = edtiableHeader.value?.innerText.trim() || '';
    headText.value = content !== '';
    // Emit the updated header value to the parent component
    emit('update:header', formattedHeader.value);
    console.log('Header updated:', formattedHeader.value);
  }
}

function updateInputDescrip() {
  if (editableDescrip.value) {
    formattedDescrip.value = editableDescrip.value.innerHTML;
    const content = editableDescrip.value?.innerText.trim() || '';
    DescripText.value = content !== '';
    emit('update:description', formattedDescrip.value);
  }
}

function handleClickOutside(event: MouseEvent) {
  const target = event.target as Node;
  const isClickInsideeditableHeader = edtiableHeader.value?.contains(event.target as Node);
  const isClickInsideToolbar = headToolbar.value?.contains(event.target as Node);
  const isInsideInput = inputRef.value?.contains(target);

  const isClickInsideeditabledescrip = editableDescrip.value?.contains(event.target as Node);
  const isClickInsideDescripToolbar = descriptoolbar.value?.contains(event.target as Node);
  const isInsideInputDescrip = inputDescripRef.value?.contains(target);

  if (!isClickInsideeditableHeader && !isClickInsideToolbar && !isInsideInput) {
    showHeadTool.value = false;
  }

  if (!isClickInsideeditabledescrip && !isClickInsideDescripToolbar && !isInsideInputDescrip) {
    showDescripTool.value = false;
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>
<style scoped>
.q-toolbar {
  width: 320px;
  background-color: none;
}
.input-wrapper {
  position: relative;
  margin: 15px;
}

.editable-div {
  padding: 12px;
  min-height: 30px;
  font-size: 16px;
  background: white;
  border-radius: 4px;
  outline: none;
  width: 100%;
}
.toolbar {
  min-height: 20px;
  max-height: 30px;
}

.slideTool {
  min-height: 10px;
  width: 320px;
}
.editable-div:empty:before {
  content: attr(data-placeholder);
  color: #aaa;
  pointer-events: none;
}
</style>
