<template>
  <q-card class="q-pa-md q-ma-md evaluate-get">
    <q-markdown class="q-ma-md title">{{ formImageData.title }}</q-markdown>
    <q-img :src="formImageData.imageUrl" fit="scale-down" :ratio="1" class="centered-image"></q-img>
  </q-card>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps<{
  title: string;
  imageUrl: string;
}>();

const emit = defineEmits(['update:title', 'update:imageUrl']);

const formImageData = ref({
  title: props.title,
  imageUrl: props.imageUrl,
});

watch(
  formImageData,
  (newValue) => {
    emit('update:title', newValue.title);
    emit('update:imageUrl', newValue.imageUrl);
  },
  { deep: true },
);
</script>

<style scoped>
.title {
  font-size: 20px;
}
.centered-image {
  display: block;
  margin: 0 auto;
  max-height: 300px;
}
</style>
