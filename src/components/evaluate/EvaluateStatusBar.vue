<template>
  <div class="header-bar bg-primary q-px-md row items-center justify-between" style="border: 0">
    <!-- Left: Title and Status -->
    <div class="col-grow">
      <div class="row item-center">
        <div class="text-white body q-my-auto">การจัดการแบบสอบถาม</div>
        <div class="q-ml-md text-white sub-body q-my-auto">กำลังบันทึก...</div>
      </div>
    </div>

    <!-- Right: Icons + Button -->
    <div class="row items-center q-gutter-sm no-wrap">
      <q-btn flat round icon="link" size="lg" class="text-white" @click="onLink" />
      <q-btn flat round icon="visibility" size="lg" class="text-white" @click="onPreview" />
      <q-btn
        unelevated
        label="เผยแพร่"
        color="accent"
        @click="onPublish"
        class="btn sub-body flex items-center justify-center publish-btn"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import router from 'src/router';

const onLink = () => {
  console.log('Link clicked');
};

const onPreview = async () => {
  await router.push('/evaluate/management/preview-evaluate');
  console.log('Preview clicked');
};

const onPublish = () => {
  console.log('Publish clicked');
};
</script>

<style scoped lang="scss">
.header-bar {
  min-height: 45px;
}

.publish-btn {
  height: 45px;
  width: 140px;
  color: white;
  font-weight: 500;
  padding: 4px 16px;
  border-radius: 8px;
  font-size: 0.85rem;
}

@media (max-width: 600px) {
  .header-bar {
    flex-direction: column;
    align-items: flex-start !important;
  }
}
</style>
