<template>
  <canvas ref="canvas"></canvas>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, defineExpose } from 'vue';
import { Chart, PieController, ArcElement, Toolt<PERSON>, Legend } from 'chart.js';

Chart.register(<PERSON><PERSON><PERSON><PERSON><PERSON>, ArcElement, Tooltip, Legend);

const props = defineProps<{
  labels: string[];
  data: number[];
}>();

const canvas = ref<HTMLCanvasElement | null>(null);
let chartInstance: Chart | null = null;

const pieColors = ['#3d3c91', '#5961b3', '#7883cc', '#a0aee6', '#c8cfff'];

function renderChart() {
  if (!canvas.value) return;
  if (chartInstance) chartInstance.destroy();

  chartInstance = new Chart(canvas.value, {
    type: 'pie',
    data: {
      labels: props.labels,
      datasets: [
        {
          data: props.data,
          backgroundColor: pieColors.slice(0, props.data.length),
          borderColor: 'white',
          borderWidth: 2,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'right',
          align: 'center',
          labels: {
            usePointStyle: true,
            boxWidth: 20,
            padding: 30,
            font: {
              size: 20,
              weight: 'bold',
            },
          },
        },
      },
      layout: { padding: 0 },
    },
  });
}

onMounted(renderChart);

watch(
  () => [props.labels, props.data],
  () => {
    renderChart();
  },
);

onBeforeUnmount(() => {
  if (chartInstance) chartInstance.destroy();
});

defineExpose({ canvas });
</script>

<style scoped>
canvas {
  width: 100% !important;
  height: 300px !important;
}
</style>
