<template>
  <div class="three-dots-container" :class="containerClass">
    <div :class="gutterClass">
      <q-icon name="fiber_manual_record" :size="dotSize" :color="dotColor" class="three-dot-item" />
      <q-icon name="fiber_manual_record" :size="dotSize" :color="dotColor" class="three-dot-item" />
      <q-icon name="fiber_manual_record" :size="dotSize" :color="dotColor" class="three-dot-item" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// Define props with defaults
const props = withDefaults(
  defineProps<{
    size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
    color?: string;
    spacing?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
    vertical?: boolean;
    centered?: boolean;
    inline?: boolean;
  }>(),
  {
    size: 'sm',
    color: 'grey-6',
    spacing: 'xs',
    vertical: false,
    centered: true,
    inline: false,
  },
);

// Computed properties for dynamic styling
const dotSize = computed(() => {
  const sizeMap = {
    xs: '8px',
    sm: '12px',
    md: '16px',
    lg: '20px',
    xl: '24px',
  };
  return sizeMap[props.size];
});

const dotColor = computed(() => props.color);

const containerClass = computed(() => {
  const classes = [];

  if (props.centered) {
    classes.push('flex', 'flex-center');
  }

  if (props.inline) {
    classes.push('inline-flex');
  }

  return classes.join(' ');
});

const gutterClass = computed(() => {
  if (props.vertical) {
    return `column items-center justify-center no-wrap q-gutter-y-${props.spacing}`;
  }
  return `row items-center justify-center no-wrap q-gutter-${props.spacing}`;
});
</script>

<style scoped>
.three-dots-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.three-dot-item {
  flex-shrink: 0;
  transition: all 0.2s ease;
}

/* Ensure proper spacing and alignment */
.row.no-wrap {
  flex-wrap: nowrap;
}

.column.no-wrap {
  flex-wrap: nowrap;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .three-dot-item {
    font-size: 0.8em;
  }
}
</style>
