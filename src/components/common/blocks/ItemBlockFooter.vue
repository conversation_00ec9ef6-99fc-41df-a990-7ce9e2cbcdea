<script setup lang="ts">
import ToggleBtn from '../ToggleBtn.vue';

defineProps<{
  label: string;
}>();

defineEmits<{
  (e: 'duplicate'): void;
  (e: 'delete'): void;
}>();

const model = defineModel({ default: true, type: Boolean, required: false });
</script>
<template>
  <div class="row items-center justify-end" style="max-height: 50px; width: 100%">
    <q-btn
      flat
      round
      icon="content_copy"
      padding="sm"
      class="bg-transparent"
      color="grey"
      @click="$emit('duplicate')"
    />
    <q-btn
      flat
      round
      icon="delete"
      color="grey"
      padding="sm"
      class="bg-transparent"
      @click="$emit('delete')"
    />
    <q-separator vertical inset color="#898989" />
    <q-item-label style="padding: 10px">{{ label }}</q-item-label>
    <ToggleBtn v-model="model" />
  </div>
</template>
<style scoped></style>
