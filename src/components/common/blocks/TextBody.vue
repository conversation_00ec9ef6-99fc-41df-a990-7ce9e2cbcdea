<template>
  <div><q-input v-model="store.textInput" placeholder="คำตอบ..." dense disable /></div>
</template>

<script setup lang="ts">
import { inject } from 'vue';
import type { ItemBlockStore } from 'src/stores/item_block_store';

// Get the store instance provided by the parent ItemBlock
const store = inject<ItemBlockStore>('blockStore');
if (!store) {
  throw new Error('TextAns must be used within an ItemBlock component');
}
</script>

<style scoped>
.q-input {
  align-items: flex-start;
  width: 360px;
}
</style>
