<template>
  <div class="q-ml-md">
    <div class="row items-center q-mb-md">
      <div class="col-8">
        <q-item-label class="q-mr-sm">อนุญาตไฟล์บางประเภทเท่านั้น</q-item-label>
      </div>
      <div class="col-4">
        <!-- ค้อยร้ปลี่ยน ToggleBtn อนาคต -->
        <q-toggle v-model="restrictFileTypes" color="primary" />
      </div>
    </div>

    <!-- Checkboxes สำหรับเลือกประเภทไฟล์ (แสดงเมื่อ toggle เปิด) -->
    <div v-if="restrictFileTypes" class="q-ml-md q-mb-md">
      <div class="row">
        <div class="col-3 q-mr-xl">
          <q-checkbox v-model="fileTypes.docx" label="เอกสาร (.docx)" color="primary" />
          <q-checkbox v-model="spreadsheetsSelected" label="สเปรตชีต (.xlsx, csv)" color="primary" />
          <q-checkbox v-model="fileTypes.pdf" label="PDF" color="primary" />
          <q-checkbox v-model="fileTypes.mp4" label="วีดิโอ" color="primary" />
        </div>
        <div class="col-3 q-mr-xl">
          <q-checkbox v-model="fileTypes.pptx" label="งานนำเสนอ (.pptx)" color="primary" />
          <q-checkbox v-model="fileTypes.gdraw" label="ภาพวาด (.gdraw)" color="primary" />
          <q-checkbox v-model="imagesSelected" label="รูปภาพ (.png, jpg, jpeg)" color="primary" />
          <q-checkbox v-model="fileTypes.mp3" label="เสียง (.mp3)" color="primary" />
        </div>
      </div>
    </div>

    <div class="row items-center q-mb-md">
      <div class="col-8"><q-item-label class="q-mr-sm">จำนวนไฟล์สูงสุด</q-item-label></div>
      <div class="col-4">
        <q-select v-model="maxFiles" :options="maxFilesOptions" dense style="width: 100px" />
      </div>
    </div>

    <div class="row items-center q-mb-md">
      <div class="col-8">
        <q-item-label class="q-mr-sm">ขนาดไฟล์สูงสุด</q-item-label>
      </div>
      <div class="col-4">
        <q-select v-model="maxFileSize" :options="maxFileSizeOptions" dense style="width: 100px" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

const restrictFileTypes = ref<boolean>(false);
const fileTypes = ref({
  docx: false,
  xlsx: false,
  csv: false,
  pdf: false,
  pptx: false,
  gdraw: false,
  png: false,
  jpg: false,
  jpeg: false,
  mp3: false,
  mp4: false,
});

const spreadsheetsSelected = computed({
  get: () => fileTypes.value.xlsx && fileTypes.value.csv,
  set: (value: boolean) => {
    fileTypes.value.xlsx = value;
    fileTypes.value.csv = value;
  },
});

const imagesSelected = computed({
  get: () => fileTypes.value.png && fileTypes.value.jpg && fileTypes.value.jpeg,
  set: (value: boolean) => {
    fileTypes.value.png = value;
    fileTypes.value.jpg = value;
    fileTypes.value.jpeg = value;
  },
});

const maxFiles = ref<number>(1);
const maxFilesOptions = [1, 5, 10];

const maxFileSize = ref<string>('1 MB');
const maxFileSizeOptions = ['1 MB', '10 MB', '100 MB', '1 GB', '10 GB'];
</script>

<style scoped>
.q-toggle {
  margin-left: 10px;
}

.q-checkbox {
  min-width: 200px;
}

.q-select {
  min-width: 10px;
}
</style>
