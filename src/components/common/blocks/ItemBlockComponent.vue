<template>
  <q-card class="q-pa-lg q-ma-md item-block-container" @click="$emit('focus-fab')">
    <!-- Header with three-dot menu -->
    <div class="item-top-bar">
      <!-- Auto-save indicator -->
      <div v-if="isSaving" class="row items-center q-gutter-xs text-grey-6 q-mr-sm">
        <q-spinner size="12px" color="grey-6" />
        <span class="text-caption">Saving...</span>
      </div>

      <!-- JSON ID Display -->
      <div class="text-caption text-grey-5 q-mr-sm">
        Block: {{ itemBlock.id }} | Assessment: {{ itemBlock.assessmentId || 'N/A' }} | Seq:
        {{ itemBlock.sequence || 'N/A' }} | Sec: {{ itemBlock.section || 'N/A' }} | Questions:
        {{ itemBlock.questions?.map((q) => q.id).join(', ') || 'N/A' }} | Options:
        {{ itemBlock.options?.map((o) => o.id).join(', ') || 'N/A' }}
      </div>

      <q-btn flat round color="grey-6" size="sm" class="three-dot-menu" @click.stop="toggleMenu">
        <ThreeDots size="xs" color="grey-6" />
        <q-menu v-model="showMenu" anchor="bottom middle" self="top middle">
          <q-list style="min-width: 150px">
            <q-item clickable v-close-popup @click="onDuplicate">
              <q-item-section avatar>
                <q-icon name="content_copy" />
              </q-item-section>
              <q-item-section>Duplicate</q-item-section>
            </q-item>
            <q-item clickable v-close-popup @click="onDelete">
              <q-item-section avatar>
                <q-icon name="delete" />
              </q-item-section>
              <q-item-section>Delete</q-item-section>
            </q-item>
          </q-list>
        </q-menu>
      </q-btn>
    </div>

    <!-- โหมดปกติ -->
    <template v-if="!showAnswerSettings">
      <div class="row">
        <div class="col">
          <EditorTool
            label="พิมพ์คำถาม..."
            v-model:content="headerText"
            @blur="saveHeaderText(headerText)"
          />
        </div>
        <div class="col-1 q-ml-sm">
          <q-btn flat round icon="image" color="grey" class="bg-transparent" padding="sm" />
        </div>
        <div class="col-auto">
          <q-select
            v-model="selectedBlockBody"
            :options="blockBodyOptions"
            filled
            dense
            style="min-width: 200px"
            color="accent"
            option-label="label"
            map-options
            @update:model-value="onBlockBodyChange"
          >
            <template v-slot:option="scope">
              <q-item v-bind="scope.itemProps">
                <q-item-section avatar>
                  <q-icon :name="scope.opt.icon" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ scope.opt.label }}</q-item-label>
                </q-item-section>
              </q-item>
            </template>
            <template v-slot:selected>
              <div class="row items-center">
                <q-icon :name="selectedBlockBody?.icon" class="q-mr-sm" />
                <div>{{ selectedBlockBody?.label }}</div>
              </div>
            </template>
          </q-select>
        </div>
      </div>

      <component :is="currentComponent" :item-block="itemBlock" class="q-mb-md q-pt-md q-ml-md" />

      <q-separator inset color="#898989" />

      <div class="row items-center justify-between q-mt-md no-wrap">
        <div class="col-auto">
          <div v-if="props.type === 'quiz'" class="row items-center q-gutter-sm">
            <q-btn
              class="text-accent"
              icon="event_available"
              label="เฉลยคำตอบ"
              @click="toggleAnswerSettings"
            />
            <div class="text-caption text-grey-7">({{ number }} point)</div>
          </div>
        </div>
        <div class="col-auto">
          <ItemBlockFooter
            label="จำเป็น"
            @duplicate="onClickDuplicateItem"
            @delete="onClickDeleteItem"
          />
        </div>
      </div>
    </template>

    <template v-else>
      <div class="row items-center text-h6 q-mb-md text-black">
        <q-icon name="event_available" class="q-mr-sm" size="md" />
        <div>เลือกคำตอบที่ถูกต้อง</div>
      </div>

      <div class="q-mb-md q-pl-sm">
        <div class="row items-center q-gutter-sm">
          <!-- ข้อความคำถาม -->
          <div class="text-subtitle1 text-weight-medium text-black">
            {{ headerText || 'คำถาม' }}
          </div>

          <q-space />
          <q-input
            v-model.number="number"
            type="number"
            filled
            :min="0"
            :max="100"
            step="1"
            style="max-width: 100px"
            dense
          />
        </div>
      </div>

      <div class="q-gutter-sm q-ml-sm q-mb-md">
        <div
          v-for="(choice, index) in itemBlock.options"
          :key="index"
          class="row items-center q-pa-sm rounded-borders cursor-pointer"
          :class="{
            'bg-green-1 text-green-10': selectedAnswer === choice.value,
            'bg-grey-2': selectedAnswer !== choice.value,
          }"
          @click="selectedAnswer = choice.value"
        >
          <q-radio
            v-model="selectedAnswer"
            :val="choice.value"
            size="sm"
            color="green"
            class="q-mr-sm"
          />

          <div class="text-body1">{{ choice.optionText }}</div>

          <!-- ไอคอนถูก -->
          <q-space />
          <q-icon v-if="selectedAnswer === choice.value" name="check" color="green" size="sm" />
        </div>
      </div>
      <!-- ปุ่ม -->
      <div class="row items-center q-mt-md">
        <div class="col-auto">
          <q-btn
            v-if="props.type === 'quiz'"
            class="text-accent"
            icon="event_available"
            label="เพิ่มคำตอบข้อเสนอแนะ"
          />
        </div>
        <q-space />
        <div class="col-auto">
          <q-btn color="accent" flat label="เสร็จสิ้น" @click="showAnswerSettings = false" />
        </div>
      </div>
    </template>
  </q-card>
</template>

<script setup lang="ts">
import { computed, ref, onUnmounted, provide } from 'vue';
import type { Component, Ref } from 'vue';
import type { DropdownItemBlockType, ItemBlock } from 'src/types/models';
import ItemBlockFooter from 'src/components/common/blocks/ItemBlockFooter.vue';
import EditorTool from 'src/components/common/EditorTool.vue';
import ThreeDots from 'src/components/common/ThreeDots.vue';
import type { BlockBodyOptionsType } from 'src/types/app';
import OptionBody from './OptionBody.vue';
import TextBody from './TextBody.vue';
import CheckBoxBody from './CheckBoxBody.vue';
import GridBody from './GridBody.vue';
import FileUploadBody from './FileUploadBody.vue';
import { blockBodyOptions } from 'src/data/blocks';
import { extractBlockBodyType } from 'src/utils/block_helper';
import { AssessmentService } from 'src/services/asm/assessmentService';
const number = ref<number>(0);
const selectedAnswer = ref<number | null>(null);
const emit = defineEmits([
  'focus-fab',
  'duplicate',
  'delete',
  'update:question',
  'update:type',
  'update:isRequired',
]);
const showAnswerSettings = ref(false);

function toggleAnswerSettings() {
  showAnswerSettings.value = !showAnswerSettings.value;
}
const props = defineProps<{
  itemBlock: ItemBlock;
  type: 'quiz' | 'evaluate';
}>();

// Make itemBlock accessible in the template
const itemBlock = props.itemBlock;

// Initialize assessment service
const assessmentService = new AssessmentService(props.type || 'evaluate');

// Auto-save state tracking
const isSaving = ref(false);
const DEBOUNCE_DELAY = 500; // 500ms debounce delay for auto-save

// Debounce timeout refs for different fields
const questionDebounceTimeout = ref<number | null>(null);
const optionDebounceTimeouts = ref<Map<number, number>>(new Map());

const headerText = ref<string>('');

// Menu state
const showMenu = ref(false);

// Initialize header text based on the itemBlock prop (if available)
if (props.itemBlock && props.itemBlock.headerBody) {
  headerText.value = props.itemBlock.headerBody.title || '';
}

// Clear existing timeout if any
function clearDebounceTimeout(timeoutRef: Ref<number | null>) {
  if (timeoutRef.value) {
    clearTimeout(timeoutRef.value);
    timeoutRef.value = null;
  }
}

// Auto-save functionality for questions
function debouncedQuestionAutoSave(questionId: number, content: string) {
  // Clear existing timeout
  clearDebounceTimeout(questionDebounceTimeout);

  // Set new timeout for auto-save
  questionDebounceTimeout.value = window.setTimeout(() => {
    if (content.trim() !== '') {
      performQuestionAutoSave(questionId, content).catch((error) => {
        console.error('Question auto-save error:', error);
      });
    }
  }, DEBOUNCE_DELAY);
}

// Auto-save functionality for options
function debouncedOptionAutoSave(
  optionId: number,
  field: 'optionText' | 'score',
  value: string | number,
) {
  // Clear existing timeout for this option
  const existingTimeout = optionDebounceTimeouts.value.get(optionId);
  if (existingTimeout) {
    clearTimeout(existingTimeout);
  }

  // Set new timeout for auto-save
  const timeoutId = window.setTimeout(() => {
    performOptionAutoSave(optionId, field, value).catch((error) => {
      console.error('Option auto-save error:', error);
    });
    optionDebounceTimeouts.value.delete(optionId);
  }, DEBOUNCE_DELAY);

  optionDebounceTimeouts.value.set(optionId, timeoutId);
}

// Perform the actual question auto-save operation
async function performQuestionAutoSave(questionId: number, content: string) {
  try {
    if (!questionId) return;

    isSaving.value = true;

    // Prepare the update payload for question
    const updatePayload = {
      questionText: content,
      itemBlockId: props.itemBlock.id,
    };

    // Call the updateQuestion API with itemBlock id and type
    await assessmentService.updateQuestion(props.itemBlock.id, props.itemBlock.type, updatePayload);
  } catch {
    // Auto-save failed silently
  } finally {
    isSaving.value = false;
  }
}

// Perform the actual option auto-save operation
async function performOptionAutoSave(
  optionId: number,
  field: 'optionText' | 'score',
  value: string | number,
) {
  try {
    if (!optionId) return;

    isSaving.value = true;

    // Prepare the update payload for option
    const updatePayload = {
      [field]: value,
      itemBlockId: props.itemBlock.id,
    };

    // Call the updateOption API with itemBlock id and type
    await assessmentService.updateOption(props.itemBlock.id, props.itemBlock.type, updatePayload);
  } catch {
    // Auto-save failed silently
  } finally {
    isSaving.value = false;
  }
}

// Cleanup function to clear timeouts when component is unmounted
onUnmounted(() => {
  clearDebounceTimeout(questionDebounceTimeout);

  // Clear all option timeouts
  optionDebounceTimeouts.value.forEach((timeoutId) => {
    clearTimeout(timeoutId);
  });
  optionDebounceTimeouts.value.clear();
});

// Expose auto-save functions for child components to use
const triggerQuestionAutoSave = (questionId: number, content: string) => {
  debouncedQuestionAutoSave(questionId, content);
};

const triggerOptionAutoSave = (
  optionId: number,
  field: 'optionText' | 'score',
  value: string | number,
) => {
  debouncedOptionAutoSave(optionId, field, value);
};

// Provide auto-save functions to child components via provide/inject
provide('autoSave', {
  triggerQuestionAutoSave,
  triggerOptionAutoSave,
  isSaving,
});

const saveHeaderText = (text: string) => {
  // * save header text to server
  console.log('Header text saved:', text);
};

const selectedBlockBody = ref<BlockBodyOptionsType>(
  extractBlockBodyType(props.itemBlock) || blockBodyOptions[0]!,
);

const onBlockBodyChange = (value: BlockBodyOptionsType) => {
  selectedBlockBody.value = value;
  // Emit the change to parent component
  emit('update:type', value);
};

// Initialize component when mounted
// onMounted(() => {
//   // If it's a grid type, set up the header question
//   if (props.selectedType === 'grid') {
//     // Set up grid questions
//     setupGridQuestions();

//     // Ensure all column choices have score set to 0
//     store?.gridColumnOptions.forEach((choice) => {
//       if (choice) {
//         choice.score = 0;
//       }
//     });

//     // If there are no row questions yet, add at least one
//     if (store?.gridRowQuestions.length === 0) {
//       store?.addRowQuestion();
//       // Set up grid questions again
//       setupGridQuestions();
//     }

//     // Make sure we have a main question text for the header
//     if (!questionTextModel.value || questionTextModel.value.trim() === '') {
//       // Set a default main question if none exists
//       emit('update:question', 'คำถามหลัก');
//       console.log('Set default grid header question on mount');
//     }
//   }
// });

const onClickDuplicateItem = () => {
  // * implement duplication logic here
};

const onClickDeleteItem = () => {
  // * implement deletion logic here
  emit('delete');
};

// Menu handler functions
function toggleMenu() {
  showMenu.value = !showMenu.value;
}

function onDuplicate() {
  emit('duplicate');
}

function onDelete() {
  emit('delete');
}

const currentComponent = computed<Component>(() => {
  if (!selectedBlockBody.value) {
    return OptionBody; // Default component if none selected
  }
  type ComponentType = Record<DropdownItemBlockType, Component>;

  const components: ComponentType = {
    RADIO: OptionBody,
    TEXTFIELD: TextBody,
    CHECKBOX: CheckBoxBody,
    GRID: GridBody,
    FILE: FileUploadBody,
  };

  return components[selectedBlockBody.value.value];
});
</script>

<style scoped>
.q-select {
  box-sizing: border-box;
  width: 255px;
  background: #fffdfd;
  border: 1px solid #b1b1b1;
  border-radius: 10px;
}

.main-question-input {
  transition: all 0.3s ease;
}

.item-block-container {
  position: relative;
}

.item-top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: -16px;
  margin-left: -32px;
  margin-right: -32px;
  margin-bottom: 8px;
}

.three-dot-menu {
  background-color: transparent;
  border: none;
  border-radius: 0;
  padding: 4px;
  cursor: grab;
  outline: none;
}

.three-dot-menu:hover {
  background-color: transparent;
}

.three-dot-menu:active {
  cursor: grabbing;
}

.three-dot-menu:focus {
  outline: none;
  box-shadow: none;
}
</style>
