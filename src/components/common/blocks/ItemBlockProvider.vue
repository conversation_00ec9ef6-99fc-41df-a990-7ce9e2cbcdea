<script setup lang="ts">
import { provide } from 'vue';
import { createItemBlockStore } from 'src/stores/item_block_store';
import type { ItemBlock } from 'src/types/models';

const props = defineProps<{
  blockId: number;
  itemBlock?: ItemBlock;
}>();

// Create the store for this block, passing the itemBlock data
const blockStore = createItemBlockStore(props.blockId, props.itemBlock);
// Provide the store to child components
provide('blockStore', blockStore());
</script>

<template>
  <slot />
</template>
