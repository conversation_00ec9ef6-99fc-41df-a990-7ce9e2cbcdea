<template>
  <div>
    <q-tabs
      narrow-indicator
      v-model="selectedTab"
      outside-arrows
      align="center"
      class="full-width bg-white"
    >
      <q-tab class="cus-tab" v-for="m in menu" :key="m.name ?? ''" :name="m.name ?? ''">
        <q-icon :name="m.icon" class="q-mr-xs" />
        {{ m.label }}
      </q-tab>
    </q-tabs>
  </div>
</template>

<script setup lang="ts">
import type { QTabProps } from 'quasar';
import { defaultAsmTabsMenu } from 'src/data/menu';
import { watch } from 'vue';
import { useRouter } from 'vue-router';

defineProps<{
  menu: QTabProps[];
}>();

const selectedTab = defineModel<string>({
  default: String(defaultAsmTabsMenu[0]?.name ?? ''),
});
const router = useRouter();

watch(
  () => selectedTab.value,
  async (value) => {
    // Use router.replace instead of push to avoid creating new history entries
    // Preserve existing query params and only update the hash
    await router.replace({
      query: router.currentRoute.value.query,
      hash: `#${value}`,
    });
  },
);

// Set the selected tab based on the current route
// selectedTab.value =
//   localMenuItems.value.find((item) => route.path.includes(item.link))?.title ||
//   localMenuItems.value[0]?.title;

// // Filter menu items based on user permissions
// const filteredMenuItems = computed(() => {
//   const user = authStore.getCurrentUser();
//   return localMenuItems.value.filter((item) => {
//     // If no permissions required, show the item
//     if (!item.perId || item.perId.length === 0) return true;

//     // Check if user has any of the required permissions
//     return user?.psnPermissions.some((perm) => item.perId.includes(perm.perId));
//   });
// });
</script>

<style scoped lang="scss">
.q-tabs {
  height: 40px;
}

.cus-tab {
  width: 7%;
  height: 40px;

  border-top-left-radius: $generic-border-radius;
  border-top-right-radius: $generic-border-radius;
  &.q-tab--active {
    color: $accent;
    background-color: $surface-primary;
  }
}
</style>
