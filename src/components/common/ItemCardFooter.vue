<script setup lang="ts">
import { withDefaults } from 'vue';
import ToggleBtn from './ToggleBtn.vue';

interface Props {
  label: string;
  isRequired?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isRequired: false,
});

defineEmits(['duplicate', 'delete', 'update:isRequired']);
</script>
<template>
  <div class="row items-center justify-end" style="border-top: 1px; max-height: 50px; width: 100%">
    \ <q-btn flat round icon="content_copy" color="grey" @click="$emit('duplicate')" />
    <q-btn flat round icon="delete" color="grey" @click="$emit('delete')" />
    <q-separator vertical inset color="#898989" />
    <q-item-label style="padding: 10px">{{ props.label }}</q-item-label>
    <ToggleBtn
      :model-value="props.isRequired"
      @update:model-value="$emit('update:isRequired', $event)"
    />
  </div>
</template>
<style scoped></style>
