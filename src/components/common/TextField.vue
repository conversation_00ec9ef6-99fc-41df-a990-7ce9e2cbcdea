<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps<{
  modelValue: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
}>();

const text = ref(props.modelValue);

watch(text, (val) => {
  emit('update:modelValue', val);
});
</script>

<template>
  <q-input v-model="text" placeholder="กรุณากรอกข้อมูล..." class="custom-input" outlined />
</template>

<style scoped>
.custom-input {
  width: 267px;
  height: 45px;
  border-radius: 12px;
  font-size: 16px;
}

/* ปรับข้อความให้อยู่กลางแนวตั้ง */
.custom-input :deep(input) {
  padding: 0 12px;
  height: 100%;
  display: flex;
}
</style>
