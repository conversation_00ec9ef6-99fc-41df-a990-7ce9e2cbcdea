<script setup lang="ts">
import { ref } from 'vue';

const props = defineProps<{
  modelValue: string;
  type?: 'radio' | 'checkbox' | 'none';
  image?: string;
}>();

const emit = defineEmits(['update:modelValue', 'delete', 'selectImage']);

const fileInputRef = ref<HTMLInputElement | null>(null);

function onFileSelected(event: Event) {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file) {
    const reader = new FileReader();
    reader.onload = () => {
      emit('selectImage', reader.result);
    };
    reader.readAsDataURL(file);
  }
}
</script>

<template>
  <q-card class="q-pa-sm row items-center rounded-borders" flat bordered>
    <q-icon name="drag_indicator" class="q-mr-sm text-grey-6" />

    <q-icon
      v-if="props.type === 'radio'"
      name="radio_button_unchecked"
      class="q-mr-sm text-grey-5"
      size="24px"
    />
    <q-icon
      v-else-if="props.type === 'checkbox'"
      name="check_box_outline_blank"
      class="q-mr-sm text-grey-5"
      size="24px"
    />

    <q-input
      :model-value="props.modelValue"
      @update:model-value="(val) => emit('update:modelValue', val)"
      placeholder="ตัวเลือกที่..."
      class="flex-1"
      borderless
      dense
    />

    <q-btn round flat class="custom-icon-btn">
      <img
        src="/svg/image.svg"
        alt="section"
        style="width: 24px; height: 24px"
        @click="fileInputRef?.click()"
      />
    </q-btn>

    <!-- input file ซ่อน -->
    <input
      ref="fileInputRef"
      type="file"
      accept="image/*"
      @change="onFileSelected"
      style="display: none"
    />

    <q-btn flat dense round icon="close" color="grey" @click="emit('delete')" />
  </q-card>
</template>

<style scoped>
.rounded-borders {
  /* Style=Default */

  box-sizing: border-box;

  /* Auto layout */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 8px 12px;
  gap: 4px;

  width: 300px;
  height: 43px;

  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 12px;

  /* Inside auto layout */
  flex: none;
  order: 0;
  align-self: stretch;
  flex-grow: 0;
}
</style>
