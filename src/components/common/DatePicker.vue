<template>
  <div class="q-pa-md" style="max-width: 300px">
    <q-input outlined v-model="proxyDate" :label="props.label" class="input">
      <template v-slot:prepend>
        <q-icon name="event" class="cursor-pointer">
          <q-popup-proxy cover transition-show="scale" transition-hide="scale">
            <q-date v-model="proxyDate" mask="YYYY-MM-DD HH:mm" color="secondary">
              <q-card-actions align="between" class="q-pa-sm">
                <q-btn flat label="ล้างค่า" @click="clearDate" color="accent" />
                <div>
                  <q-btn v-close-popup label="ยกเลิก" color="accent" flat />
                  <q-btn flat label="ตกลง" v-close-popup color="accent" />
                </div>
              </q-card-actions>
            </q-date>
          </q-popup-proxy>
        </q-icon>
      </template>

      <template v-slot:append>
        <q-icon name="access_time" class="cursor-pointer">
          <q-popup-proxy cover transition-show="scale" transition-hide="scale">
            <q-time v-model="proxyDate" mask="YYYY-MM-DD HH:mm" format24h color="secondary">
              <q-card-actions align="between" class="q-pa-sm">
                <q-btn flat label="ล้างค่า" @click="clearDate" color="accent" />
                <div>
                  <q-btn v-close-popup label="ยกเลิก" color="accent" flat />
                  <q-btn flat label="ตกลง" v-close-popup color="accent" />
                </div>
              </q-card-actions>
            </q-time>
          </q-popup-proxy>
        </q-icon>
      </template>
    </q-input>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useQuasar } from 'quasar';
import langTh from 'quasar/lang/th';

// ตั้งค่า locale ภาษาไทย
const $q = useQuasar();
$q.lang.set(langTh);

// รับ props และ emit
const props = defineProps<{
  label: string;
  modelValue: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
}>();

// ตัวแปรภายใน component
const proxyDate = ref(props.modelValue);

// watch เพื่ออัปเดตค่ากลับไปยัง parent component
watch(proxyDate, (val) => {
  emit('update:modelValue', val);
});

// ล้างค่า date
function clearDate() {
  proxyDate.value = '';
}
</script>

<style scoped>
.input {
  /* DateTime Picker */

  box-sizing: border-box;

  width: 249px;
  height: 52px;

  background: #ffffff;
  border-radius: 12px;

  /* Inside auto layout */
  flex: none;
  order: 2;
  flex-grow: 0;
}
</style>
