<script setup lang="ts">
import { ref } from 'vue';
import { useImageStore } from 'stores/image';

const showPanel = ref(false);
const imageStore = useImageStore();
const menuStyle = ref({
  left: '100%',
  top: '0px',
});

const tempWidth = ref(imageStore.widthPixel);
const tempHeight = ref(imageStore.heightPixel);

function applySize() {
  showPanel.value = false;
}

function cancelSize() {
  imageStore.widthPixel = tempWidth.value;
  imageStore.heightPixel = tempHeight.value;
  showPanel.value = false;
}
</script>

<template>
  <div class="relative-position" style="background-color: transparent">
    <!-- ปุ่มจุดสามจุด -->
    <q-btn
      round
      flat
      fab
      icon="more_vert"
      @click="showPanel = !showPanel"
      padding="md"
      class="bg-grey-3"
      style="border-radius: 99px"
    />

    <!-- กล่องกรอกขนาด (แสดงใต้ปุ่ม) -->
    <q-slide-transition>
      <div v-show="showPanel" class="menu-box absolute" :style="menuStyle">
        <q-card class="q-pa-sm" style="width: 300px; z-index: 10">
          <q-card-section class="scroll" style="max-height: 40vh">
            <div class="row items-center">
              <span class="label" style="min-width: 80px; color: black">ความกว้าง</span>
              <q-input v-model="imageStore.widthPixel" outlined dense class="pixel-input" />
              <span class="label q-ml-md" style="color: black">พิกเซล</span>
            </div>

            <div class="row items-center q-mt-md">
              <span class="label" style="min-width: 80px; color: black">ความสูง</span>
              <q-input v-model="imageStore.heightPixel" outlined dense class="pixel-input" />
              <span class="label q-ml-md" style="color: black">พิกเซล</span>
            </div>
          </q-card-section>

          <q-card-actions align="left" class="row justify-between">
            <q-btn flat label="ยกเลิก" style="color: black" @click="cancelSize" />
            <q-btn flat label="ยืนยัน" color="primary" @click="applySize" />
          </q-card-actions>
        </q-card>
      </div>
    </q-slide-transition>
  </div>
</template>

<style scoped>
.pixel-input {
  max-width: 100px;
  font-size: 12px;
}
.menu-box {
  z-index: 1000;
}
</style>
