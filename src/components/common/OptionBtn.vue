<script setup lang="ts">
const props = defineProps<{
  label: string;
  icon?: string;
  selected?: boolean;
}>();

const emit = defineEmits<{
  (e: 'click'): void;
}>();
</script>

<template>
  <q-btn
    class="float-btn"
    :class="{ selected: props.selected }"
    :label="props.label"
    :icon="props.icon"
    @click="emit('click')"
    no-caps
    rounded
    unelevated
    padding="12px 24px"
  />
</template>

<style scoped>
.float-btn {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  padding: 12px 24px;
  gap: 10px;
  width: 144px;
  height: 45px;
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  color: #000000;
  transition:
    background 0.3s,
    color 0.3s;
}

.float-btn.selected {
  background: #8a64b2;
  color: #ffffff;
}
</style>
