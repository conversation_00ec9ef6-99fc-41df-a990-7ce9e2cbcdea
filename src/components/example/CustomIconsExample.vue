<template>
  <div class="q-pa-md">
    <div class="text-h6">Custom SVG Icons Example</div>

    <div class="q-gutter-md q-mt-md">
      <!-- Individual icons with different sizes and colors -->
      <div class="row items-center q-gutter-md">
        <q-icon name="app:add-circle" size="2rem" color="primary" />
        <q-icon name="app:image" size="2rem" color="secondary" />
        <q-icon name="app:text" size="2rem" color="accent" />
        <q-icon name="app:section" size="2rem" color="positive" />
      </div>

      <!-- Using custom icons in Quasar components -->
      <div class="row items-center q-gutter-md">
        <q-btn icon="app:add-circle" label="Add Circle" color="primary" />
        <q-btn icon="app:image" label="Image" color="secondary" />
        <q-btn icon="app:text" label="Text" color="accent" />
        <q-btn icon="app:section" label="Section" color="positive" />
      </div>

      <!-- Different sizes example -->
      <div class="row items-center q-gutter-md">
        <q-icon name="app:add-circle" size="1rem" color="primary" />
        <q-icon name="app:add-circle" size="1.5rem" color="primary" />
        <q-icon name="app:add-circle" size="2rem" color="primary" />
        <q-icon name="app:add-circle" size="2.5rem" color="primary" />
        <q-icon name="app:add-circle" size="3rem" color="primary" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Custom SVG icons are mapped in src/boot/icons.ts
// and can be used with the 'app:' prefix
</script>
