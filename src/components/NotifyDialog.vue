<script lang="ts" setup>
import { useAuthStore } from 'src/stores/auth'

const authStore = useAuthStore()
</script>

<template>
  <q-dialog v-model="authStore.notifyDialog" persistent>
    <q-card class="q-pa-md text-center fontSarabun" style="width: 400px; max-width: 90vw">
      <div class="text-h6 text-weight-bold q-mb-sm text-warning">แจ้งเตือน</div>

      <q-icon name="warning" size="48px" color="orange" class="q-mb-md" />

      <div class="q-mb-md">
        <strong>{{ authStore.notifyMessage }}</strong>
      </div>

      <q-btn label="ยืนยัน" color="primary" class="btnSave" data-cy="notidlg_ok_btn"
        @click="authStore.notifyDialog = false" unelevated />
    </q-card>
  </q-dialog>
</template>
