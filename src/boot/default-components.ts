import { QTable } from 'quasar';
import type { ComponentConstructor, QTableProps } from 'quasar';
import { boot } from 'quasar/wrappers';

export default boot(() => {
  //   SetComponentDefaults<QInputProps>(QInput, {
  //     outlined: true,
  //     dense: true,
  //     stackLabel: true,
  //   });
  //   SetComponentDefaults<QSelectProps>(QSelect, {
  //     outlined: true,
  //     dense: true,
  //     stackLabel: true,
  //   });
  SetComponentDefaults<QTableProps['pagination']>(QTable, {
    sortBy: 'id',
    page: 1,
    rowsPerPage: 10,
    descending: false, //ascending
    rowsNumber: 0,
  });
});

/**
 * Set some default properties on a component
 */

const SetComponentDefaults = <T>(
  component: ComponentConstructor<T>,
  defaults: Partial<T>,
): void => {
  (Object.keys(defaults) as (keyof typeof defaults)[]).forEach((prop: keyof typeof defaults) => {
    component.props[prop] =
      Array.isArray(component.props[prop]) === true || typeof component.props[prop] === 'function'
        ? {
            type: component.props[prop],
            default: defaults[prop],
          }
        : {
            ...component.props[prop],
            default: defaults[prop],
          };
  });
};
