import { Test, TestingModule } from '@nestjs/testing';
import { GraylogController } from './graylog.controller';
import { GraylogService } from './graylog.service';

describe('GraylogController', () => {
  let controller: GraylogController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [GraylogController],
      providers: [GraylogService],
    }).compile();

    controller = module.get<GraylogController>(GraylogController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
