const routes = [
  {
    path: '/ums/ums-permission',
    name: 'ums_permission',
    components: {
      default: () => import('src/pages/ums/ums_permission/UmsPermissionView.vue'),
      menu: () => import('src/components/ums/MainMenu.vue'),
    },
    meta: {
      layout: 'MainLayout',
      ums: true,
      perms: [1],
    },
  },
  {
    path: '/ums/ums-permission/add',
    name: 'ums_permission_add',
    components: {
      default: () => import('src/pages/ums/ums_permission/UmsPermissonFormView.vue'),
      menu: () => import('src/components/ums/MainMenu.vue'),
    },
    meta: {
      layout: 'MainLayout',
      ums: true,
      perms: [1],
    },
  },
  {
    path: '/ums/ums-permission/edit/:perId',
    name: 'ums_permission_edit',
    components: {
      default: () => import('src/pages/ums/ums_permission/UmsPermissonFormView.vue'),
      menu: () => import('src/components/ums/MainMenu.vue'),
    },
    meta: {
      layout: 'MainLayout',
      ums: true,
      perms: [1],
    },
  },
];

export default routes;
