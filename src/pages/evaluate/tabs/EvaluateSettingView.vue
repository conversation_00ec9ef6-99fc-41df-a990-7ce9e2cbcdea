<script setup lang="ts">
import { computed, ref, watch, onMounted, onActivated, onDeactivated, onUnmounted } from 'vue';
import { useEvaluateFormStore } from 'src/stores/evaluate/form';
import SelectDate from 'src/components/common/SelectDate.vue';
import Toggle from 'src/components/common/ToggleBtn.vue';
import { useRoute } from 'vue-router';

// const router = useRouter();
const route = useRoute();
const paramId = route.params.id as string;
const evaluateFormStore = useEvaluateFormStore();

// Create reactive refs for the form fields
const startDate = ref('');
const endDate = ref('');
const responseEdit = ref(false);
const subLimit = ref<number | null>(null);
const globalIsRequired = ref(false); // Added for global toggle of isRequired
const isPrototype = ref(false);

// Initialize form fields from assessment
// ! implemented new service later
onMounted(async () => {
  await evaluateFormStore.fetchAssessmentById(Number(paramId));
  if (evaluateFormStore.currentAssessment) {
    startDate.value = evaluateFormStore.currentAssessment.startAt || '';
    endDate.value = evaluateFormStore.currentAssessment.endAt || '';
    responseEdit.value = evaluateFormStore.currentAssessment.responseEdit || false;
    subLimit.value =
      evaluateFormStore.currentAssessment.submitLimit !== undefined
        ? evaluateFormStore.currentAssessment.submitLimit
        : null;
    globalIsRequired.value = evaluateFormStore.currentAssessment.globalIsRequired || false;
    isPrototype.value = evaluateFormStore.currentAssessment.isPrototype || false;
  } else {
    console.log('No assessment in store, creating default assessment');
    startDate.value = '';
    endDate.value = '';
    responseEdit.value = false;
    subLimit.value = null;
  }
});

onUnmounted(() => {
  console.log('SettingEvaluate component unmounted, saving assessment');
  if (evaluateFormStore.currentAssessment) {
    evaluateFormStore.currentAssessment = null;
  } else {
    console.warn('No assessment to save on unmount');
  }
});

// Watch for changes in the assessment and update local state
watch(
  () => evaluateFormStore.currentAssessment,
  (newAssessment) => {
    if (newAssessment) {
      // Update local state with new assessment values
      startDate.value = newAssessment.startAt || '';
      endDate.value = newAssessment.endAt || '';
      responseEdit.value = newAssessment.responseEdit || false;
      subLimit.value = newAssessment.submitLimit !== undefined ? newAssessment.submitLimit : null;
      globalIsRequired.value = newAssessment.globalIsRequired || false;
    }
  },
  { deep: true }, // Add deep watching to detect nested property changes
);

// Update the assessment when form fields change
watch(startDate, (newValue) => {
  if (evaluateFormStore.currentAssessment) {
    console.log('Start date changed:');
    // Handle empty string case
    if (newValue) {
      evaluateFormStore.currentAssessment.startAt = newValue;
    } else {
      delete evaluateFormStore.currentAssessment.startAt;
    }
    void saveAssessment();
  }
});

watch(endDate, (newValue) => {
  if (evaluateFormStore.currentAssessment) {
    console.log('End date changed:');
    // Handle empty string case
    if (newValue) {
      evaluateFormStore.currentAssessment.endAt = newValue;
    } else {
      delete evaluateFormStore.currentAssessment.endAt;
    }
    void saveAssessment();
  }
});

watch(responseEdit, (newValue) => {
  if (evaluateFormStore.currentAssessment) {
    console.log('Response edit changed:');
    // Ensure we're setting a boolean value
    evaluateFormStore.currentAssessment.responseEdit = Boolean(newValue);
    void saveAssessment();
  }
});

watch(subLimit, (newValue) => {
  if (evaluateFormStore.currentAssessment) {
    console.log('Submission limit changed:');
    evaluateFormStore.currentAssessment.submitLimit = Number(newValue);
    void saveAssessment();
  }
});

// Watch for changes in the global isRequired toggle
watch(globalIsRequired, (newValue) => {
  if (evaluateFormStore.currentAssessment) {
    console.log('Global isRequired changed:');
    // Update the globalIsRequired property in the assessment
    evaluateFormStore.currentAssessment.globalIsRequired = Boolean(newValue);
    // Update all itemBlocks to match the global setting
    if (evaluateFormStore.currentAssessment.itemBlocks) {
      evaluateFormStore.currentAssessment.itemBlocks.forEach((itemBlock) => {
        // Only update non-header and non-image blocks
        if (itemBlock.type !== 'HEADER' && itemBlock.type !== 'IMAGE') {
          itemBlock.isRequired = Boolean(newValue);
        }
      });
      console.log('Updated isRequired for all itemBlocks:', newValue);
    }
    void saveAssessment();
  }
});

watch(isPrototype, (newValue) => {
  if (evaluateFormStore.currentAssessment) {
    console.log('Prototype status changed:');
    // Ensure we're setting a boolean value
    evaluateFormStore.currentAssessment.isPrototype = Boolean(newValue);
    void saveAssessment();
  }
});

// Toggle submission limit
const limitOneSubmission = computed({
  get: () => {
    return subLimit.value === 1;
  },
  set: (value: boolean) => {
    subLimit.value = value ? 1 : -1;
  },
});

// Function to save the assessment
const saveAssessment = async () => {
  if (evaluateFormStore.currentAssessment) {
    try {
      // Create a copy of the assessment to ensure all properties are included
      const assessmentToSave = { ...evaluateFormStore.currentAssessment };

      // Ensure all required fields are set with proper types
      if (startDate.value) {
        assessmentToSave.startAt = startDate.value;
      } else {
        delete assessmentToSave.startAt;
      }

      if (endDate.value) {
        assessmentToSave.endAt = endDate.value;
      } else {
        delete assessmentToSave.endAt;
      }
      assessmentToSave.submitLimit = Number(subLimit.value);
      assessmentToSave.responseEdit = Boolean(responseEdit.value);
      assessmentToSave.globalIsRequired = Boolean(globalIsRequired.value);
      assessmentToSave.isPrototype = Boolean(isPrototype.value);
      await evaluateFormStore.updateAssessment(assessmentToSave.id, assessmentToSave);
      console.log('Assessment saved successfully');
    } catch (error) {
      console.error('Error saving assessment:', error);
    }
  } else {
    console.warn('No assessment to save');
  }
};

// Handle keep-alive activation
onActivated(() => {
  console.log('SettingEvaluate component activated');
  // Refresh data from store if needed
  if (evaluateFormStore.currentAssessment) {
    console.log('Refreshing settings from store on activation');
    startDate.value = evaluateFormStore.currentAssessment.startAt || '';
    endDate.value = evaluateFormStore.currentAssessment.endAt || '';
    responseEdit.value = evaluateFormStore.currentAssessment.responseEdit || false;
    subLimit.value =
      evaluateFormStore.currentAssessment.submitLimit !== undefined
        ? evaluateFormStore.currentAssessment.submitLimit
        : null;
    globalIsRequired.value = evaluateFormStore.currentAssessment.globalIsRequired || false;
  }
});

// Handle keep-alive deactivation
onDeactivated(() => {
  console.log('SettingEvaluate component deactivated, saving assessment');
  // Save changes to store
  void saveAssessment();
});
</script>

<template>
  <q-page class="q-pa-md">
    <q-card class="evaluate-item" style="border-radius: 10px; max-height: 100vh; overflow-y: auto">
      <!-- Header -->
      <q-card-section class="row items-center justify-between">
        <div class="text-h5 text-weight-bold">ตั้งค่า</div>
        <!-- <q-btn flat dense icon="close" size="lg" @click="router.back()" /> -->
      </q-card-section>

      <q-separator class="q-mt-md" />

      <q-card-section class="column q-gutter-md">
        <div class="row items-start justify-between">
          <div>
            <div class="text-h5 text-weight-bold">กำหนดขอบเขตเวลา</div>
            <div class="text-grey text-h10">กำหนดวันที่เพื่อเปิด-ปิดแบบสอบถามอัตโนมัติ</div>
          </div>

          <div class="row q-gutter-sm">
            <SelectDate
              :model-value="startDate"
              @update:model-value="startDate = $event"
              label="วันที่เริ่มต้น"
            />
            <SelectDate
              :model-value="endDate"
              @update:model-value="endDate = $event"
              label="วันที่สิ้นสุด"
              :disable="startDate === '' || startDate === null"
              :rules="[
                (val: any) => {
                  return val >= startDate || 'วันที่สิ้นสุดต้องมากกว่าหรือเท่ากับวันที่เริ่มต้น';
                },
              ]"
            />
          </div>
        </div>
        <q-separator class="q-mt-md" />

        <!-- Responses -->
        <q-expansion-item :model-value="responseEdit || limitOneSubmission" header-class="q-pa-none justify-start">
          <template #header>
            <q-item-section>
              <div class="text-h5 text-weight-bold">การตอบกลับ</div>
              <div class="text-grey text-h10">กำหนดวิธีการตอบกลับของผู้ตอบ</div>
            </q-item-section>
          </template>
          <div class="column">
            <div class="sub-row">
              <div>
                <div class="text-h6">สามารถแก้ไขคำตอบ</div>
              </div>
              <Toggle v-model:model-value="responseEdit" />
            </div>
            <div class="sub-row">
              <div>
                <div class="text-h6">จำกัดการส่งได้แค่ครั้งเดียว</div>
              </div>
              <Toggle v-model:model-value="limitOneSubmission" />
            </div>
          </div>
        </q-expansion-item>

        <q-separator class="q-mt-md" />

        <!-- Default Questions -->
        <q-expansion-item :model-value="globalIsRequired" header-class="q-pa-none justify-start">
          <template #header>
            <q-item-section>
              <div>
                <div class="text-h5 text-weight-bold">ค่าตั้งต้นของคำถาม</div>
                <div class="text-grey text-h10">กำหนดค่าตั้งต้นของคำถามทั้งหมดของแบบสอบถามนี้</div>
              </div>
            </q-item-section>
          </template>
          <div class="column">
            <div class="sub-row">
              <div>
                <div class="text-h6">จำเป็นต้องตอบทุกข้อ</div>
              </div>
              <Toggle v-model:model-value="globalIsRequired" />
            </div>
          </div>
        </q-expansion-item>
        <q-separator class="q-mt-md" />
        <div class="row items-start justify-between">
          <div>
            <div class="text-h5 text-weight-bold">เป็นต้นแบบ</div>
            <div class="text-grey text-h10">กำหนดให้ฟอร์มนี้เป็นต้นแบบสำหรับสำเนาเท่านั้น</div>
          </div>
          <div class="q-mt-md">
            <Toggle v-model:model-value="isPrototype" />
          </div>
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>
<style scoped>
.sub-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px; /* q-mb-md */
  margin-top: 16px; /* q-mt-md */
  margin-left: 24px; /* q-ml-lg */
}
</style>
