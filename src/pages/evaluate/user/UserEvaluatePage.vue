<template>
  <q-page class="q-pa-md">
    <div v-if="editFormData">
      <div
        v-for="(evaluateItem, index) in editFormData.itemBlocks?.filter(
          (item) => item.section === currentSection,
        )"
        :key="index"
      >
        <UserTextBlock
          v-if="evaluateItem.headerBody"
          :item="evaluateItem"
          :title="title || 'ไม่มีข้อมูล'"
          :description="description || 'ไม่มีข้อมูล'"
        />
      </div>

      <div
        v-for="(evaluateItem, index) in editFormData.itemBlocks?.filter(
          (item) => item.section === currentSection,
        )"
        :key="index"
      >
        <UserQuestionBlock
          :id="evaluateItem.id"
          :item="editFormData"
          :category="evaluateItem.type"
          :section="evaluateItem.section"
          :status="Preview"
          @update-answer="handleAnswer"
        />
        <UserImageBlock
          v-if="evaluateItem.type === 'IMAGE'"
          :title="evaluateItem.headerBody?.title"
          :image-url="evaluateItem.imageBody?.imagePath"
          @update:title="evaluateItem.headerBody && (evaluateItem.headerBody.title = $event)"
          @update:image-url="evaluateItem.imageBody && (evaluateItem.imageBody.imagePath = $event)"
        />
      </div>
    </div>

    <div class="row q-pa-md btn-footer">
      <div class="col">
        <MyButton label="ล้างแบบสอบถาม" class="btn-clear" @click="clearForm" />
      </div>
      <div class="col-auto q-pr-lg">
        <MyButton v-if="currentSection > 1" label="กลับไปหน้าก่อน" @click="previousSection" />
      </div>
      <MyButton v-show="hideButton" label="หน้าต่อไป" @click="nextSection" />

      <div class="col-auto">
        <MyButton v-if="!hideButton" label="ส่งแบบสอบถาม" @click="submitForm" />
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import UserTextBlock from 'src/components/evaluate/UserEvaluateBlock/UserTextBlock.vue';
// import UserImageBlock from 'src/components/evaluate/UserEvaluateBlock/UserImageBlock.vue';
import UserQuestionBlock from 'src/components/evaluate/UserEvaluateBlock/UserQuestionBlock.vue';
import MyButton from 'src/components/common/MyButton.vue';
import { computed, onMounted, ref, watch, watchEffect } from 'vue';
import form from 'src/services/evaluate/form';
import type { Assessment } from 'src/types/models';
import router from 'src/router';
import { useRoute } from 'vue-router';

const route = useRoute();
const editFormData = ref<Assessment>();
const checkPage = ref<Assessment>();

const evaluateId = ref(0);
const currentSection = ref(0);
const Preview = ref('');

watch(
  () => route.params,
  (params) => {
    evaluateId.value = Number(params.id);
    currentSection.value = Number(params.section);
    Preview.value = String(params.status);
  },
  { immediate: true },
);

onMounted(async () => {
  const res = await form.getOneBySection(Number(route.params.id), Number(route.params.section));
  editFormData.value = res.data;
});

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const answers = ref<{ [key: string]: any }>({});

type AnswerValue = string | string[] | Record<string, number>;

const handleAnswer = (payload: { id: string | number; value: AnswerValue }) => {
  answers.value[payload.id] = payload.value;
  console.log(answers.value);
};

watchEffect(() => {
  evaluateId.value = Number(route.query.id) || 0;
  currentSection.value = Number(route.query.section) || 1;
  Preview.value = String(route.query.status) || '';
});

const hideButton = ref(false);

// โหลดข้อมูลเมื่อ section หรือ id เปลี่ยน
watch(
  () => route.params,
  async (params) => {
    const id = Number(params.id);
    const section = Number(params.section);

    if (!id || !section) return;

    evaluateId.value = id;
    currentSection.value = section;
    Preview.value = String(params.status);

    try {
      const res = await form.getOneBySection(id, section);
      editFormData.value = res.data;

      const check = await form.getOneBySection(id, section + 1);
      checkPage.value = check.data;
      if (checkPage.value) {
        hideButton.value = true;
      } else {
        hideButton.value = false;
      }
    } catch (e) {
      console.error('โหลดข้อมูลไม่สำเร็จ', e);
    }
  },
  { immediate: true },
);

// เปลี่ยน section และโหลดข้อมูล พร้อม push query
const updateSection = async (newSection: number) => {
  currentSection.value = newSection;
  if (Preview.value === 'preview') {
    await router.push({
      name: 'preview-evaluate',
      params: {
        id: evaluateId.value,
        section: currentSection.value,
        status: Preview.value,
      },
    });
  } else if (Preview.value === 'user-do') {
    await router.push({
      name: 'user-do-evaluate',
      params: {
        id: evaluateId.value,
        section: currentSection.value,
        status: Preview.value,
      },
    });
  }
};

const nextSection = async () => {
  await updateSection(currentSection.value + 1);
};

const previousSection = async () => {
  if (currentSection.value > 1) {
    currentSection.value--;
    await updateSection(currentSection.value);
  }
};
const STORAGE_KEY = 'draft-form';
const clearForm = () => {
  localStorage.removeItem(STORAGE_KEY); // ลบค่าที่เก็บไว้
};

const submitForm = () => {
  console.log('Submitting form');
};
const title = computed(
  () =>
    editFormData.value?.itemBlocks?.find((item) => item.section === 1)?.headerBody?.title?.trim() ||
    'ไม่มีข้อมูล',
);

const description = computed(
  () =>
    editFormData.value?.itemBlocks
      ?.find((item) => item.section === 1)
      ?.headerBody?.description?.trim() || 'ไม่มีข้อมูล',
);
</script>

<style scoped lang="scss">
.btn-footer {
  margin: auto;
  max-width: 900px;
  min-width: 900px;
  width: 100%;
}

.btn-clear {
  background-color: white;
  color: $primary;
}
</style>
