<template>
  <q-page class="q-pa-md">
    <div class="q-pa-md flex flex-center" style="margin-top: 1%">
      <div style="width: 100%; max-width: 1000px">

        <div v-if="!isSubmitted">
          <div v-if="!hasQuestion">
            <q-card class="column" style="padding: 32px; min-height: 500px; height: 100%">
              <div>
                <div class="row justify-between items-center">
                  <div class="text-h6 text-primary">{{ quiz.name }}</div>
                  <div class="text-h6 text-black">คะแนนเต็ม: {{ quiz.totalScore }}</div>
                </div>
                <div class="sub-body" style="color: black; margin-top: 50px">
                  แบบทดสอบ {{ quiz.name }}
                </div>
              </div>
              <q-space />
              <div class="row justify-end">
                <q-btn color="primary" class="text-black" label="เริ่มทำแบบทดสอบ" @click="startQuiz" />
              </div>
            </q-card>
          </div>

          <div v-else class="q-my-md">
            <div v-if="currentItemBlock">
              <div class="row justify-between items-center">
                <div class="text-h6 text-black">คำถาม {{ currentQuestionNo }}</div>
                <div class="text-h6 text-black">
                  เหลือ {{ Math.floor(timeLeft / 60) }}:{{ String(timeLeft % 60).padStart(2, '0') }} นาที
                </div>
              </div>

              <div class="row justify-between items-start q-mt-md">
                <div class="text-h6 text-primary" style="margin-top: 8%">
                  {{ currentItemBlock.questions?.[0]?.questionText }}
                </div>
              </div>

              <q-card class="q-pa-md q-mt-md column" style="width: 100%; padding: 30px" bordered>

                <template v-if="currentItemBlock.type === QuestionType.RADIO">
                  <div v-for="option in currentItemBlock.options" :key="option.id" class="q-mb-sm">
                    <div class="q-pa-sm rounded-borders bg-grey-1 hover-card"
                      :class="{ selected: selected === option.id }">
                      <q-radio v-model="selected" :val="option.id" :label="option.optionText" class="full-width" />
                    </div>
                  </div>
                </template>

                <template v-else-if="currentItemBlock.type === QuestionType.CHECKBOX">
                  <div v-for="option in currentItemBlock.options" :key="option.id" class="q-mb-sm">
                    <div class="q-pa-sm rounded-borders bg-grey-1 hover-card"
                      :class="{ selected: selectedMultiple.includes(option.id) }">
                      <q-checkbox v-model="selectedMultiple" :val="option.id" :label="option.optionText"
                        class="full-width" />
                    </div>
                  </div>
                </template>

                <template v-if="currentItemBlock.type === QuestionType.TEXTFIELD">
                  <q-input class="q-mt-md" label="ตอบเพิ่มเติม" v-model="textAnswer" type="textarea" />
                </template>
              </q-card>

              <div class="row justify-between items-center" style="margin-top: 80px">
                <q-btn color="secondary" label="ย้อน" class="text-white" style="width: 150px"
                  :disable="currentQuestionNo === 1" @click="goToPrevQuestion" />
                <q-btn v-if="!isLastQuestion" label="ถัดไป" color="primary" class="text-black" style="width: 150px"
                  @click="goToNextQuestion" />
                <q-btn v-else label="เสร็จสิ้น" color="positive" class="text-white" style="width: 150px"
                  @click="submitQuiz" />
              </div>
            </div>
            <div v-else>
              <q-banner class="text-negative bg-red-1 q-mt-md">ไม่พบคำถาม</q-banner>
            </div>
          </div>
        </div>

        <div v-else>
          <q-card bordered class="q-pa-md column q-mt-md items-center justify-center"
            style="width: 100%; margin-top: 60px; padding: 30px; min-height: 300px">
            <div class="text-h6 text-primary q-mb-sm">ท่านได้คะแนนทั้งหมด</div>
            <div class="text-h4 text-positive q-mb-sm">{{ totalCorrect }}</div>
            <div class="text-h6 text-primary q-mb-md">จากคะแนนเต็ม {{ totalQuestions }} คะแนน</div>
            <div class="sub-body surface q-mb-xs">
              ใช้เวลาไป
              <template v-if="timeUsed.minutes > 0">
                {{ timeUsed.minutes }} นาที
              </template>
              <template v-if="timeUsed.seconds > 0">
                {{ timeUsed.seconds }} วินาที
              </template>
              <template v-if="timeUsed.minutes === 0 && timeUsed.seconds === 0">
                น้อยกว่า 1 วินาที
              </template>
            </div>

            <div class="sub-body">บันทึกเมื่อ {{ new Date().toLocaleString() }}</div>
          </q-card>
          <div class="row justify-center q-mt-md">
            <q-btn color="primary" label="กลับหน้าหลัก" style="width: 150px" @click="router.push('/')" />
          </div>
        </div>
      </div>
    </div>
    <DialogQuiz v-model="showDialog" @confirm="submitQuiz" />
  </q-page>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { ref, computed } from 'vue'
import DialogQuiz from 'src/components/quiz/DialogQuiz.vue'
import { QuestionType } from 'src/types/quiz'
const route = useRoute()
const router = useRouter()
const startTimestamp = ref<number | null>(null)
const hasQuestion = computed(() => !!route.query.no)
const currentQuestionNo = computed(() => Number(route.query.no || 1))
const showDialog = ref(false)
const quiz = {
  id: 1,
  name: "Frontend Developer Quiz",
  totalScore: 100,
  timeout: 10,
  itemBlocks: Array.from({ length: 10 }, (_, i) => {
    const id = i + 1
    const types = [QuestionType.RADIO, QuestionType.CHECKBOX, QuestionType.TEXTFIELD]
    const type = types[i % 3]

    const optionSets = [
      ["<h1>", "<div>", "<p>"],
      ["const", "var", "let"],
      ["useState", "setTimeout", "querySelector"],
      ["===", "==", "="],
      ["npm install", "npm push", "npm run"],
      ["CSS", "HTML", "Python"],
      ["div", "head", "meta"],
      ["React", "Node.js", "MongoDB"],
      ["class", "id", "style"],
      ["document.getElementById", "console.log", "alert"],
    ]
    const values = [1, 0, 0]
    const questionTexts = [
      "Which HTML tag is used for headings?",
      "Which keyword is used to declare a constant in JavaScript?",
      "Which React hook is used to manage state?",
      "Which operator compares both value and type in JavaScript?",
      "Which command installs packages using npm?",
      "Which language is used for styling web pages?",
      "Which tag is a block-level element?",
      "Which JavaScript library is used for building UI components?",
      "Which attribute is used to apply CSS styles to an element?",
      "Which method selects an element by ID in JavaScript?",
    ]

    return {
      id,
      type,
      questions: [{
        id: 10 + id,
        itemBlockId: id,
        questionText: questionTexts[i],
        isHeader: false,
        sequence: 1,
      }],
      options: type !== QuestionType.TEXTFIELD
        ? (optionSets[i] ?? []).map((text, index) => ({
          id: 100 + id * 10 + index,
          itemBlockId: id,
          optionText: text,
          value: values[index],
          sequence: index + 1,
        }))
        : []
    }
  })
}

const totalQuestions = quiz.itemBlocks.length

const currentItemBlock = computed(() => {
  const idx = currentQuestionNo.value - 1
  return quiz.itemBlocks[idx] || null
})

const isLastQuestion = computed(() => currentQuestionNo.value >= totalQuestions)

const answers = ref(
  Array.from({ length: totalQuestions }, () => ({
    choice: null,
    choices: [] as number[],
    text: ''
  }))
)

const timeUsed = computed(() => {
  if (!startTimestamp.value) return { minutes: 0, seconds: 0 }
  const elapsedMs = Date.now() - startTimestamp.value
  const totalSeconds = Math.floor(elapsedMs / 1000)
  const minutes = Math.floor(totalSeconds / 60)
  const seconds = totalSeconds % 60
  return { minutes, seconds }
})

const selected = computed({
  get: () => answers.value[currentQuestionNo.value - 1]?.choice ?? null,
  set: (val) => {
    const a = answers.value[currentQuestionNo.value - 1]
    if (a) a.choice = val
  }
})

const selectedMultiple = computed({
  get: () => answers.value[currentQuestionNo.value - 1]?.choices ?? [],
  set: (val) => {
    const a = answers.value[currentQuestionNo.value - 1]
    if (a) a.choices = val
  }
})

const textAnswer = computed({
  get: () => answers.value[currentQuestionNo.value - 1]?.text ?? '',
  set: (val) => {
    const a = answers.value[currentQuestionNo.value - 1]
    if (a) a.text = val
  }
})

const timeLeft = ref(quiz.timeout)
let timer: ReturnType<typeof setInterval> | null = null

function startCountdown() {
  if (timer) clearInterval(timer)
  timer = setInterval(() => {
    if (timeLeft.value > 0) {
      timeLeft.value--
    } else {
      clearInterval(timer!)
      showDialog.value = true
      submitQuiz()
    }
  }, 1000)
}

async function startQuiz() {
  answers.value = Array.from({ length: totalQuestions }, () => ({
    choice: null,
    choices: [],
    text: ''
  }))
  timeLeft.value = quiz.timeout
  startTimestamp.value = Date.now()
  isSubmitted.value = false
  totalCorrect.value = 0
  startCountdown()
  await router.push({ path: route.path, query: { no: 1 } })
}

async function goToNextQuestion() {
  const nextNo = currentQuestionNo.value + 1
  await router.push({ path: route.path, query: { no: nextNo } })
}

async function goToPrevQuestion() {
  const prevNo = Math.max(1, currentQuestionNo.value - 1)
  await router.push({ path: route.path, query: { no: prevNo } })
}

const isSubmitted = ref(false)
const totalCorrect = ref(0)

function submitQuiz() {
  if (timer) clearInterval(timer)

  const results = answers.value.map((ans, i) => {
    const block = quiz.itemBlocks[i]
    let isCorrect = false

    if (block?.type === QuestionType.RADIO) {
      const correct = block.options.find(o => o.value === 1)
      isCorrect = ans.choice === correct?.id
    } else if (block?.type === QuestionType.CHECKBOX) {
      const correctIds = block.options.filter(o => o.value === 1).map(o => o.id).sort()
      const selectedIds = [...ans.choices].sort()
      isCorrect = JSON.stringify(correctIds) === JSON.stringify(selectedIds)
    } else if (block?.type === QuestionType.TEXTFIELD) {
      isCorrect = ans.text.trim().length > 0
    }

    return { isCorrect }
  })

  totalCorrect.value = results.filter(r => r.isCorrect).length
  isSubmitted.value = true
}



</script>


<style lang="scss" scoped>
$accent-selected: #f1ebf8;

.bg-accent-selected {
  background-color: $accent-selected;
  border-radius: 12px;
}

.hover-card {
  transition: background-color 0.3s ease;
  cursor: pointer;

  &:hover {
    background-color: #e0e0e0;
  }
}

.selected {
  background-color: hsl(46, 98%, 78%) !important;
}
</style>
