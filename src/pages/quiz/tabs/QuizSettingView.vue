<template>
  <div class="row justify-center q-mt-xl">
    <q-card class="q-pa-xl custom-card">
      <div class="text-h4 q-mb-lg text-weight-bold">ตั้งค่า</div>

      <!-- แต่ละ block = row -->
      <div class="q-gutter-md column">
        <!-- Row 1 -->
        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">กำหนดขอบเขตเวลา</div>
            <div class="text-caption text-grey">
              กำหนดวันที่และเวลาเพื่อเปิด-ปิดแบบทดสอบแบบอัตโนมัติ
            </div>
          </div>
          <div class="row q-col-gutter-sm items-center">
            <DatePicker v-model="selectedDate" label="เลือกวันเริ่มต้น" />
            <div>-</div>
            <DatePicker
              v-model="selectedDateEnd"
              label="เลือกวันสิ้นสุด"
              style="margin-right: 20px"
            />
          </div>
        </div>

        <!-- Row 2 -->
        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">ตั้งเวลาทำแบบทดสอบ</div>
            <div class="text-caption text-grey">กำหนดเวลาในการทำแบบทดสอบของผู้ทำแบบสอบถาม</div>
          </div>
          <div class="row q-col-gutter-sm items-center">
            <HourDropdown v-model="hour" />
            <MinDropdown v-model="minute" style="margin-right: 30px" />
          </div>
        </div>

        <!-- Row 3 -->
        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">จำนวนครั้งที่สามารถทำแบบทดสอบ</div>
            <div class="text-caption text-grey">
              กำหนดจำนวนครั้งที่สามารถทำแบบทดสอบของผู้ทำแบบสอบถาม
            </div>
          </div>
          <div style="min-width: 300px">
            <TextField v-model="attemptLimit" placeholder="กรุณากรอกข้อมูล..." />
          </div>
        </div>

        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">ร้อยละขั้นต่ำเพื่อผ่านแบบทดสอบ</div>
            <div class="text-caption text-grey">กำหนดเกณฑ์คะแนนผ่านของแบบสอบถาม</div>
          </div>
          <div style="min-width: 300px">
            <TextField v-model="passPercentage" placeholder="กรุณากรอกข้อมูล..." />
          </div>
        </div>

        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">เป็นต้นแบบ</div>
            <div class="text-caption text-grey">กำหนดให้ฟอร์มนี้เป็นต้นแบบสำหรับสำเนาเท่านั้น</div>
          </div>
          <div style="min-width: 80px">
            <Toggle :model-value="false" @update:model-value="() => {}" />
          </div>
        </div>
      </div>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import DatePicker from 'src/components/common/DatePicker.vue';
import HourDropdown from 'src/components/common/HourDropdown.vue';
import TextField from 'src/components/common/TextField.vue';
import Toggle from 'src/components/common/ToggleBtn.vue';
import MinDropdown from 'src/components/common/MinDropdown.vue';
const hour = ref<number | null>(null);

const minute = ref<number | null>(null);

const attemptLimit = ref('');
const passPercentage = ref('');
const selectedDate = ref('');
const selectedDateEnd = ref('');
</script>

<style scoped>
.custom-card {
  width: 1100px;
  height: 600px;

  border-radius: 12px;

  /* Inside auto layout */
  flex: none;
  order: 0;
  flex-grow: 0;
}
</style>
