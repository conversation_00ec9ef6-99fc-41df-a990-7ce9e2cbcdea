<template>
  <!-- Auto-save status indicator -->
  <div class="auto-save-status">
    <!-- <q-badge v-if="saveStatus === 'saving'" color="grey" text-color="white">
          <q-spinner size="xs" color="white" class="q-mr-xs" /> สำเร็จ
        </q-badge>
        <q-badge v-else-if="saveStatus === 'error'" color="negative" text-color="white">
          บันทึกล้มเหลว
        </q-badge> -->
  </div>
  <BlockCreator :blocks="blocks" type="quiz" />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import type { ItemBlock } from 'src/types/models';
import { AssessmentService } from 'src/services/asm/assessmentService';
import BlockCreator from 'src/components/common/blocks/BlockCreator.vue';
import { defaultBlocks } from 'src/data/defaultBlocks';

const blocks = ref<ItemBlock[]>([...defaultBlocks]);

// Store and state management
const route = useRoute();
// const formId = ref<number | null>(null);
// const isNewForm = computed(() => formId.value === null);
// const isSaving = ref(false);
// const saveStatus = ref<'idle' | 'saving' | 'success' | 'error'>('idle');
// const saveError = ref<string | null>(null);
// const lastSavedTime = ref<Date | null>(null);

// // Form data references
// const formTitle = ref('');
// const formDescription = ref('');

// Initialize form data if editing existing form
onMounted(async () => {
  const id = route.params.id ? Number(route.params.id) : null;

  if (id) {
    try {
      const res = await new AssessmentService('quiz').fetchOne(id);
      // Use existing data if available, otherwise use default blocks
      blocks.value = res.itemBlocks && res.itemBlocks.length > 0 ? res.itemBlocks : defaultBlocks;
    } catch (error) {
      console.error('Failed to load form:', error);
      // On error, use default blocks
      blocks.value = defaultBlocks;
    }
  } else {
    // For new forms, use default blocks
    blocks.value = defaultBlocks;
  }
});

// Collect form data from all components
// const collectFormData = (): {
//   id?: number;
//   title?: string;
//   description?: string;
//   programId: number; // Added programId field
//   questions?: Array<{ id: number; text: string | undefined; type: string | undefined }>;
//   images?: Array<{ id: number; data: string | undefined }>;
//   evaluateItems: [];
// } => {
//   const questionBlocks = blocks.value.filter((b) => b.type === 'item');
//   const imageBlocks = blocks.value.filter((b) => b.type === 'image');

//   return {
//     ...(formId.value !== null ? { id: formId.value } : {}),
//     ...(formTitle.value ? { title: formTitle.value } : {}),
//     ...(formDescription.value ? { description: formDescription.value } : {}),
//     evaluateItems: [],
//     programId: 1, // Always include programId with value 1
//     ...(questionBlocks.length > 0
//       ? {
//           questions: questionBlocks.map((block) => ({
//             id: block.id,
//             text: block.questionText,
//             type: block.selectedType,
//           })),
//         }
//       : {}),
//     ...(imageBlocks.length > 0
//       ? {
//           images: imageBlocks.map((block) => ({
//             id: block.id,
//             data: block.imageData,
//           })),
//         }
//       : {}),
//   };
// };

// Debounced auto-save function
// const autoSave = debounce(() => {
//   if (isSaving.value) return;

//   saveStatus.value = 'saving';
//   isSaving.value = true;
//   saveError.value = null;

//   try {
//     const formData = collectFormData();
//     console.log('Saving form data:', formData); // Log the data being saved

//     if (isNewForm.value) {
//       // Create new form
//       console.log('Creating new form...');
//       // const newForm = await evaluateFormStore.addForm(formData);
//     } else if (formId.value !== null) {
//       // Update existing form - only if we have a valid ID
//       console.log(`Updating existing form ID: ${formId.value}`);
//       // const updatedForm = await evaluateFormStore.updateForm(formId.value, formData);
//     }

//     saveStatus.value = 'success';
//     lastSavedTime.value = new Date();
//     console.log('Save successful at:', lastSavedTime.value);

//     // Reset status after a delay
//     setTimeout(() => {
//       if (saveStatus.value === 'success') {
//         saveStatus.value = 'idle';
//       }
//     }, 3000);
//   } catch (error) {
//     console.error('Auto-save failed:', error);
//     saveStatus.value = 'error';
//     saveError.value = error instanceof Error ? error.message : 'Failed to save form';
//     console.log('Save error:', saveError.value);
//   } finally {
//     isSaving.value = false;
//   }
// }, 500);
</script>

<style scoped>
.fixed-fab-col {
  width: 48px;
}

.section-container {
  position: relative;
  z-index: 1;
  margin-bottom: 0;
}

.section-tab {
  background-color: #673ab7;
  color: white;
  font-weight: 500;
  padding: 6px 16px;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  font-size: 14px;
  width: fit-content;
  position: relative;
  left: 0;
}

.no-top-left-radius {
  border-top-left-radius: 0 !important;
}

.auto-save-status {
  position: fixed;
  top: 10px;
  right: 10px;
  z-index: 1000;
  padding: 5px;
}
</style>
