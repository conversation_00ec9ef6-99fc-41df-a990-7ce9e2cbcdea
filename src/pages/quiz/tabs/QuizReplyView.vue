<template>
  <q-page class="q-pa-md bg-transparent">
    <div class="text-h6 q-mb-md">Quiz Dashboard</div>

    <div v-if="route.params.id === 'new'" class="text-center q-my-xl text-grey">
      <q-icon name="info" size="2em" />
      <div class="text-h6 q-mt-sm">ยังไม่มีข้อมูลผลการตอบ เนื่องจากยังไม่สร้างแบบทดสอบจริง</div>
    </div>
    <div v-else>
      <div v-if="isPageLoading && !dashboardStore.hasQuizData" class="text-center q-my-xl">
        <q-spinner-dots color="primary" size="3em" />
        <div class="text-subtitle1 q-mt-sm">Loading dashboard data...</div>
      </div>

      <q-banner v-if="overallError" inline-actions class="text-white bg-red q-mb-md">
        An error occurred while loading dashboard data: {{ overallError }}
        <template v-slot:action>
          <q-btn
            flat
            color="white"
            label="Retry All"
            @click="retryFetchAll"
            :loading="isPageLoading"
          />
        </template>
      </q-banner>

      <!-- Display content only if not initial loading OR if some data already exists -->
      <div v-if="!isPageLoading || dashboardStore.hasQuizData">
        <div v-if="dashboardStore.quizSummary">
          <q-card class="q-pa-md q-mb-md custom-light-shadow" style="border-radius: 20px">
            <div class="text-subtitle1">
              ชื่อแบบทดสอบ:
              <span class="text-weight-medium">{{ dashboardStore.quizSummary.quizTitle }}</span>
              (ID: {{ dashboardStore.quizSummary.quizId }})
            </div>
          </q-card>

          <div class="row q-gutter-md q-mb-xl q-mt-lg justify-start no-wrap">
            <q-card
              class="col-auto custom-light-shadow"
              bordered
              style="width: 32%; min-width: 300px; border-radius: 16px"
            >
              <q-card-section class="q-pa-md">
                <div class="text-body1 text-weight-medium text-left">
                  มีผู้เข้าทำแบบทดสอบนี้แล้ว (Unique Users) :
                </div>
                <br />
                <div class="text-h4 text-center" style="font-style: bold">
                  {{ dashboardStore.quizSummary.numberOfAttempts }}
                </div>
                <div
                  class="text-body2 text-grey-7"
                  style="position: absolute; bottom: 16px; right: 16px"
                >
                  คน
                </div>
              </q-card-section>
              <q-inner-loading :showing="dashboardStore.isLoadingSummary" />
            </q-card>
            <q-card
              class="col-auto custom-light-shadow"
              bordered
              style="width: 32%; min-width: 300px; border-radius: 16px"
            >
              <q-card-section class="q-pa-md">
                <div class="text-body1 text-weight-medium text-left">
                  คะแนนสูงสุดที่ผู้ทำแบบทดสอบทำได้ :
                </div>
                <br />
                <div class="text-h4 text-center" style="font-style: bold">
                  {{ dashboardStore.quizSummary.highestScore }}
                </div>
                <div
                  class="text-body2 text-grey-7"
                  style="position: absolute; bottom: 16px; right: 16px"
                >
                  คะแนน
                </div>
              </q-card-section>
              <q-inner-loading :showing="dashboardStore.isLoadingSummary" />
            </q-card>
            <q-card
              class="col-auto custom-light-shadow"
              bordered
              style="width: 32%; min-width: 300px; border-radius: 16px"
            >
              <q-card-section class="q-pa-md">
                <div class="text-body1 text-weight-medium text-left">
                  คะแนนต่ำสุดที่ผู้ทำแบบทดสอบทำได้ :
                </div>
                <br />
                <div class="text-h4 text-center" style="font-style: bold">
                  {{ dashboardStore.quizSummary.lowestScore }}
                </div>
                <div
                  class="text-body2 text-grey-7"
                  style="position: absolute; bottom: 16px; right: 16px"
                >
                  คะแนน
                </div>
              </q-card-section>
              <q-inner-loading :showing="dashboardStore.isLoadingSummary" />
            </q-card>
          </div>
        </div>
      </div>
      <div
        v-else-if="
          !dashboardStore.isLoadingSummary &&
          !dashboardStore.errorSummary &&
          dashboardStore.currentQuizId !== null
        "
        class="text-center q-my-md"
      >
        <p>No summary data available for this quiz, or quiz not selected.</p>
      </div>

      <q-card
        v-if="dashboardStore.currentQuizId !== null"
        bordered
        elevation="4"
        class="q-pa-md"
        style="border-radius: 16px"
      >
        <q-tabs
          v-model="tab"
          dense
          class="text-black"
          active-color="accent"
          indicator-color="accent"
          align="center"
          narrow-indicator
        >
          <q-tab name="dashboard" label="ภาพรวม" />
          <q-tab name="answers" label="รายข้อ" />
          <q-tab name="users" label="รายชื่อผู้เข้าร่วม" />
        </q-tabs>
        <!-- <q-separator class="q-my-sm" /> -->
        <q-tab-panels v-model="tab" animated>
          <q-tab-panel name="dashboard">
            <QuizDashboardView :quiz-id="dashboardStore.currentQuizId" />
          </q-tab-panel>
          <q-tab-panel name="answers">
            <QuizAnswersView :quiz-id="dashboardStore.currentQuizId" />
          </q-tab-panel>
          <q-tab-panel name="users">
            <QuizTakingListView :quiz-id="dashboardStore.currentQuizId" />
          </q-tab-panel>
        </q-tab-panels>
      </q-card>
      <div v-else-if="!isPageLoading && !overallError" class="text-center q-my-xl">
        <q-icon name="help_outline" size="3em" class="text-grey" />
        <div class="text-h6 q-mt-sm text-grey">
          Please select a Quiz or ensure Quiz ID is provided.
        </div>
        <p class="text-grey">No quiz data to display.</p>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import { useRoute } from 'vue-router';

import { useQuizStore } from 'src/stores/quiz';
import QuizAnswersView from '../panels/QuizAnswersView.vue';
import QuizTakingListView from '../panels/QuizTakingListView.vue';
import QuizDashboardView from '../panels/QuizDashboardView.vue';

const route = useRoute();

const quizId = route.params.id;

const tab = ref('answers');
// const router = useRouter(); // For testing selector, can remove
const dashboardStore = useQuizStore();

if (quizId === 'new') {
  // ไม่ต้องโหลดข้อมูล เพราะยังไม่สร้าง quiz จริง
  dashboardStore.setQuizId(null);
}

// For testing with a selector, remove if quizId always comes from route
// const selectedQuizIdForTest = ref<number | null>(null);

// Computed property for overall page loading state
// Considers initial loading before any specific data part is fetched or if store overallLoading is true
const isPageLoading = computed(() => {
  // If quizId is set but no summary yet, and overall store loading is true
  if (
    dashboardStore.currentQuizId !== null &&
    !dashboardStore.quizSummary &&
    dashboardStore.overallLoading
  ) {
    return true;
  }
  // If no quizId is set yet, and we are trying to fetch
  if (dashboardStore.currentQuizId === null && dashboardStore.overallLoading) {
    return true;
  }
  return dashboardStore.overallLoading;
});

// Computed property for a general error message on the page
const overallError = computed(() => {
  if (dashboardStore.errorSummary) return `Summary: ${dashboardStore.errorSummary}`;
  if (dashboardStore.errorUserAttempts) return `User Attempts: ${dashboardStore.errorUserAttempts}`;
  if (dashboardStore.errorAnswerSummary)
    return `Answer Summary: ${dashboardStore.errorAnswerSummary}`;
  // Check for a generic "Quiz ID not set" if no specific error but also no ID
  if (dashboardStore.currentQuizId === null && !dashboardStore.overallLoading) {
    // You might want a more specific message or rely on UI state
  }
  return null;
});

function parseQuizIdFromRoute(): number | null {
  const idParam = route.params.id;
  if (typeof idParam === 'string') {
    const num = Number(idParam);
    return isNaN(num) ? null : num;
  } else if (Array.isArray(idParam) && typeof idParam[0] === 'string') {
    // Handle cases where params might be an array
    const num = Number(idParam[0]);
    return isNaN(num) ? null : num;
  }
  return null;
}

async function initializeDashboard(quizIdToLoad: number | null) {
  dashboardStore.setQuizId(quizIdToLoad);

  if (quizIdToLoad !== null) {
    await dashboardStore.fetchAllQuizDataForCurrentId();
  }
}

async function retryFetchAll() {
  if (dashboardStore.currentQuizId !== null) {
    await dashboardStore.fetchAllQuizDataForCurrentId();
  } else {
    const quizIdFromRoute = parseQuizIdFromRoute();
    await initializeDashboard(quizIdFromRoute);
  }
}

onMounted(async () => {
  const quizIdFromRoute = parseQuizIdFromRoute();
  await initializeDashboard(quizIdFromRoute);
});

watch(
  () => route.params.id,
  async (newIdParam, oldIdParam) => {
    if (newIdParam !== oldIdParam) {
      const newQuizId = parseQuizIdFromRoute();
      if (dashboardStore.currentQuizId !== newQuizId) {
        await initializeDashboard(newQuizId);
      }
    }
  },
);
</script>
<style scoped>
:deep(.q-tab .q-tab__label) {
  font-size: 1.1rem;
  font-weight: 500;
  text-transform: none;
  padding-top: 4px;
  padding-bottom: 4px;
}
.custom-light-shadow {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.19);
}
</style>
