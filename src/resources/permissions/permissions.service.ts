import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { Permission } from './entities/permission.entity';
import { CreatePermissionDto } from './dto/create-permission.dto';
import { UpdatePermissionDto } from './dto/update-permission.dto';
import { DataTablePaginate } from 'src/types/DataTablePaginate';

@Injectable()
export class PermissionsService {
  constructor(
    @InjectRepository(Permission)
    private permissionRepository: Repository<Permission>,
  ) {}

  async create(createPermissionDto: CreatePermissionDto) {
    const permission = this.permissionRepository.create(createPermissionDto);
    return this.permissionRepository.save(permission);
  }

  findAll() {
    return this.permissionRepository.find();
  }

  findAllActive() {
    return this.permissionRepository.find({ where: { perStatus: true } });
  }

  async findPermissions(query): Promise<DataTablePaginate> {
    const page = query.page || 1;
    const itemsPerPage = query.itemsPerPage || 10;
    const skip = (page - 1) * itemsPerPage;
    const sortKey = query.sortBy ? query.sortBy[0].key : 'id';
    const sortOrder = query.sortBy ? query.sortBy[0].order : 'asc';
    const { name } = query.search || {};

    const whereConditions = [];
    if (name) {
      whereConditions.push({ perNameTh: Like(`%${name}%`) });
      whereConditions.push({ perNameEn: Like(`%${name}%`) });
    }

    const [permissions, total] = await this.permissionRepository.findAndCount({
      where: whereConditions.length > 0 ? whereConditions : undefined,
      order: { [sortKey]: sortOrder },
      take: itemsPerPage,
      skip: skip,
    });

    return {
      data: permissions,
      total: total,
      currentPage: page,
      itemsPerPage: itemsPerPage,
    };
  }

  async findOne(id: number) {
    const permission = await this.permissionRepository.findOne({ where: { id } });
    if (!permission) {
      throw new NotFoundException('Permission not found');
    }
    return permission;
  }

  async update(id: number, updatePermissionDto: UpdatePermissionDto) {
    const permission = await this.findOne(id);
    const updated = { ...permission, ...updatePermissionDto };
    return this.permissionRepository.save(updated);
  }

  async remove(id: number) {
    const permission = await this.findOne(id);
    return this.permissionRepository.softRemove(permission);
  }
}
