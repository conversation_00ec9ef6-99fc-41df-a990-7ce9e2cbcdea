import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Assessment } from '../../assessment-forms/entities/assessment.entity';
import { User } from 'src/resources/users/entities/user.entity';

@Entity('programs')
export class Program {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'text' })
  name: string;

  @Column({ type: 'text' })
  description: string;

  @ManyToOne(() => User, (user) => user.programs)
  @JoinColumn({ name: 'creator' })
  creator: User;

  @OneToMany(() => Assessment, (assessment) => assessment.program)
  assessments: Assessment[];
}
