import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  Query,
  HttpCode,
  HttpStatus,
  NotFoundException,
} from '@nestjs/common';
import { RolesService } from './roles.service';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';

@Controller('roles') // Base path e.g., /api/roles
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  // @Post()
  // @HttpCode(HttpStatus.CREATED)
  // create(@Body() createRoleDto: CreateRoleDto) {
  //   return this.rolesService.create(createRoleDto);
  // }

  // @Get()
  // findAll(@Query('loadUsers') loadUsers?: string) {
  //   const shouldLoadUsers = loadUsers === 'true';
  //   return this.rolesService.findAll(shouldLoadUsers);
  // }

  // @Get(':id')
  // findOne(
  //   @Param('id', ParseIntPipe) id: number,
  //   @Query('loadUsers') loadUsers?: string,
  // ) {
  //   const shouldLoadUsers = loadUsers === 'true';
  //   return this.rolesService.findOne(id, shouldLoadUsers);
  // }

  // @Get('name/:name')
  // findOneByName(
  //   @Param('name') name: string,
  //   @Query('loadUsers') loadUsers?: string,
  // ) {
  //   const shouldLoadUsers = loadUsers === 'true';
  //   const role = this.rolesService.findOneByName(name, shouldLoadUsers);
  //   if (!role) {
  //     throw new NotFoundException(`Role with name "${name}" not found`);
  //   }
  //   return role;
  // }

  // @Patch(':id')
  // update(
  //   @Param('id', ParseIntPipe) id: number,
  //   @Body() updateRoleDto: UpdateRoleDto,
  // ) {
  //   return this.rolesService.update(id, updateRoleDto);
  // }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.rolesService.remove(id);
  }
}
