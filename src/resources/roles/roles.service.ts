import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Role } from './entities/role.entity';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
// User entity might be needed if you plan to load users with roles here,
// but for basic Role CRUD, it's not strictly required to inject UserRepository.
// import { User } from 'src/resources/user/entities/user.entity';

@Injectable()
export class RolesService {
  constructor(
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
  ) {}

  // async create(createRoleDto: CreateRoleDto): Promise<Role> {
  //   const { name } = createRoleDto;
  //   const existingRole = await this.roleRepository.findOneBy({  });
  //   if (existingRole) {
  //     throw new ConflictException(`Role with name "${name}" already exists.`);
  //   }
  //   const newRole = this.roleRepository.create({ name });
  //   return this.roleRepository.save(newRole);
  // }

  async findAll(loadUsers: boolean = false): Promise<Role[]> {
    const relationsToLoad = loadUsers ? ['users'] : [];
    return this.roleRepository.find({ relations: relationsToLoad });
  }

  async findOne(id: number, loadUsers: boolean = false): Promise<Role> {
    const relationsToLoad = loadUsers ? ['users'] : [];
    const role = await this.roleRepository.findOne({
      where: { id },
      relations: relationsToLoad,
    });
    if (!role) {
      throw new NotFoundException(`Role with ID "${id}" not found`);
    }
    return role;
  }

  // async findOneByName(
  //   name: string,
  //   loadUsers: boolean = false,
  // ): Promise<Role | null> {
  //   const relationsToLoad = loadUsers ? ['users'] : [];
  //   const role = await this.roleRepository.findOne({
  //     where: { name },
  //     relations: relationsToLoad,
  //   });
  //   return role;
  // }

  // async update(id: number, updateRoleDto: UpdateRoleDto): Promise<Role> {
  //   const role = await this.findOne(id);

  //   if (updateRoleDto.name && updateRoleDto.name !== role.name) {
  //     const existingRoleWithNewName = await this.roleRepository.findOneBy({
  //       name: updateRoleDto.name,
  //     });
  //     if (existingRoleWithNewName && existingRoleWithNewName.id !== id) {
  //       throw new ConflictException(
  //         `Role with name "${updateRoleDto.name}" already exists.`,
  //       );
  //     }
  //     role.name = updateRoleDto.name;
  //   }

  //   return this.roleRepository.save(role);
  // }

  async remove(id: number): Promise<void> {
    const role = await this.findOne(id);
    await this.roleRepository.remove(role);
  }
}
