import { Controller } from '@nestjs/common';
import { OptionsService } from './options.service';
import { Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import type { CreateOptionDto } from '../dto/creates/create-option.dto';
import type { Option } from '../entities/option.entity';

@ApiTags('ASM - Options')
@Controller('options')
export class OptionsController {
  constructor(private readonly optionsService: OptionsService) {}

  @Post()
  async createOption(createOptionDto: CreateOptionDto): Promise<Option> {
    const { itemBlockId } = createOptionDto;
    return this.optionsService.create({ itemBlockId: itemBlockId });
  }
}
