import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  OneToOne,
  <PERSON>inC<PERSON>umn,
} from 'typeorm';
import { ItemBlock } from './item-block.entity';

@Entity('header_bodies')
export class HeaderBody {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  title: string;

  @Column({ nullable: true })
  description: string | null;

  @Column()
  itemBlockId: number;

  @OneToOne(() => ItemBlock, (itemBlock) => itemBlock.headerBody)
  @JoinColumn({ name: 'itemBlockId' })
  itemBlock: ItemBlock;
}
