import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Assessment } from './assessment.entity';
import { Response } from './response.entity';
import { User } from 'src/resources/users/entities/user.entity';

@Entity('submissions')
export class Submission {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'datetime' })
  startAt: Date;

  @Column({ type: 'datetime' })
  endAt: Date;

  @Column()
  userId: number;

  @Column()
  assessmentId: number;

  @ManyToOne(() => User, (user) => user.submissions)
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => Assessment, (assessment) => assessment.submissions)
  @JoinColumn({ name: 'assessmentId' })
  assessment: Assessment;

  @OneToMany(() => Response, (response) => response.submission)
  responses: Response[];
}
