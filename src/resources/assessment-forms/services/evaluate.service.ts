import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Assessment } from '../entities/assessment.entity';
import { Repository } from 'typeorm';
import { AssessmentType } from '../enums/assessment-type.enum';
import { v4 as uuidv4 } from 'uuid';
import { User } from 'src/resources/users/entities/user.entity';
import { Program } from 'src/resources/programs/entities/program.entity';
import { CreateAssessmentDto } from '../dto/creates/create-assessment.dto';
import { ItemBlocksService } from '../item-blocks/item-blocks.service';
import { ItemBlockType } from '../enums/item-block-type.enum';
import { AssessmentConfig } from 'src/configs/assessment.config';
import { HeaderBodiesService } from '../header-bodies/header-bodies.service';

@Injectable()
export class EvaluateService {
  constructor(
    @InjectRepository(Assessment)
    private readonly assessmentRepository: Repository<Assessment>,
    private readonly itemBlocksService: ItemBlocksService,
    private readonly headerBodiesService: HeaderBodiesService,
  ) {}
  async createOne(dto: CreateAssessmentDto) {
    const { creatorUserId, programId } = dto;

    const saved = await this.assessmentRepository.save({
      name: AssessmentConfig.FORM_UNTITLED,
      type: AssessmentType.EVALUATE,
      linkURL: uuidv4(),
      creator: { id: creatorUserId } as User,
      program: { id: programId } as Program,
    });

    const firstItemBlock = await this.itemBlocksService
      .createEmptyBlock({
        sequence: 1,
        section: 1,
        type: ItemBlockType.HEADER,
        assessmentId: saved.id,
      })
      .then(async (block) => {
        await this.headerBodiesService.createHeaderBody(block.id);
        return block;
      });

    // Return all required Assessment properties, filling missing ones with null or empty as needed
    return {
      ...saved,
      creator: saved.creator, // likely just id
      program: saved.program, // likely just id
      itemBlocks: [firstItemBlock],
      submissions: [], // default empty array
      isPrototype: saved.isPrototype ?? false, // fallback if not present
    } as Assessment;
  }

  // getAll(programId: number, pagination: DataParams) {
  //   const { sortBy, order, limit, page, search } = pagination;

  //   const query = this.assessmentRepo
  //     .createQueryBuilder('assessment')
  //     .leftJoinAndSelect('assessment.creator', 'creator')
  //     .where('assessment.type = :type', { type: AssessmentType.EVALUATE })
  //     .andWhere('assessment.programId = :programId', { programId });
  //   if (search) {
  //     query.andWhere('assessment.name LIKE :search', { search: `%${search}%` });
  //   }

  //   if (sortBy) {
  //     query.orderBy(`assessment.${sortBy}`, order);
  //   }

  //   query.skip((page - 1) * limit).take(limit);

  //   return query.getMany();
  // }

  // getAllName() {
  //   return this.assessmentRepo.find({
  //     select: ['id', 'name'],
  //     order: { id: 'ASC' },
  //   });
  // }

  // async findOne(id: number, section: number) {
  //   if (!id || isNaN(id)) {
  //     throw new BadRequestException('Invalid assessment ID');
  //   }

  //   return this.assessmentRepo
  //     .createQueryBuilder('assessment')
  //     .leftJoinAndSelect('assessment.itemBlocks', 'itemBlocks')
  //     .leftJoinAndSelect('itemBlocks.questions', 'questions')
  //     .leftJoinAndSelect('itemBlocks.options', 'options')
  //     .leftJoinAndSelect('itemBlocks.headerBody', 'headerBody')
  //     .where('assessment.id = :id', { id })
  //     .andWhere('assessment.type = :type', { type: AssessmentType.EVALUATE })
  //     .andWhere('itemBlocks.section = :section', { section })
  //     .getOne();
  // }

  // async update(id: number, updateAssessmentDto: UpdateAssessmentDto) {
  //   const assessment = await this.assessmentRepo.findOne({
  //     where: { id },
  //     relations: ['program', 'creator'],
  //   });

  //   if (!assessment) {
  //     throw new NotFoundException(`Assessment with ID ${id} not found`);
  //   }

  //   // Merge the existing assessment with the incoming DTO
  //   const updated = this.assessmentRepo.merge(assessment, updateAssessmentDto);

  //   // Save the merged entity
  //   await this.assessmentRepo.save(updated);

  //   return updated;
  // }

  // async remove(id: number) {
  //   const assessment = await this.assessmentRepo.findOne({
  //     where: { id },
  //     relations: [
  //       'itemBlocks',
  //       'itemBlocks.questions',
  //       'itemBlocks.options',
  //       'itemBlocks.headerBody',
  //       'itemBlocks.imageBody',
  //       'submissions',
  //       'submissions.responses',
  //     ],
  //   });

  //   if (!assessment) {
  //     throw new NotFoundException(`Assessment with ID ${id} not found`);
  //   }

  //   // ลบ responses
  //   for (const submission of assessment.submissions) {
  //     if (submission.responses.length) {
  //       await this.assessmentRepo.manager.remove(submission.responses);
  //     }
  //   }

  //   // ลบ submissions
  //   if (assessment.submissions.length) {
  //     await this.assessmentRepo.manager.remove(assessment.submissions);
  //   }

  //   // ลบ questions
  //   for (const block of assessment.itemBlocks) {
  //     if (block.questions.length) {
  //       await this.assessmentRepo.manager.remove(block.questions);
  //     }
  //   }

  //   // ลบ options
  //   for (const block of assessment.itemBlocks) {
  //     if (block.options.length) {
  //       await this.assessmentRepo.manager.remove(block.options);
  //     }
  //   }

  //   // ลบ headerBody & imageBody (OneToOne)
  //   for (const block of assessment.itemBlocks) {
  //     if (block.headerBody) {
  //       await this.assessmentRepo.manager.remove(block.headerBody);
  //     }
  //     if (block.imageBody) {
  //       await this.assessmentRepo.manager.remove(block.imageBody);
  //     }
  //   }

  //   // ลบ itemBlocks
  //   if (assessment.itemBlocks.length) {
  //     await this.assessmentRepo.manager.remove(assessment.itemBlocks);
  //   }

  //   // ลบ assessment ตัวเอง
  //   await this.assessmentRepo.remove(assessment);

  //   return {
  //     success: true,
  //     message: `Assessment #${id} and related data deleted.`,
  //   };
  // }
}
