import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SubmissionsService } from './submissions.service';
import { Submission } from '../entities/submission.entity';
import { Response } from '../entities/response.entity';
import { SubmissionsController } from './controllers/submissions.controller';
import { QuizSubmissionsController } from './controllers/quiz.submissions.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Submission,
      Response, // <-- Added Response entity
    ]),
  ],
  controllers: [SubmissionsController, QuizSubmissionsController],
  providers: [SubmissionsService],
  exports: [SubmissionsService],
})
export class SubmissionsModule {}
