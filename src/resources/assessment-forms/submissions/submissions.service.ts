import { Injectable } from '@nestjs/common';
import type { UpdateSubmissionDto } from '../dto/updates/update-submission.dto';
import type { CreateSubmissionDto } from '../dto/creates/create-submission.dto';
import { StartQuizDto } from '../dto/start-quiz.dto';
import { Submission } from '../entities/submission.entity';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { Assessment } from '../entities/assessment.entity';

@Injectable()
export class SubmissionsService {
  @InjectEntityManager()
  private entityManager: EntityManager;

  create(createSubmissionDto: CreateSubmissionDto) {
    return 'This action adds a new submission';
  }

  findAll() {
    return `This action returns all submissions`;
  }

  findOne(id: number) {
    return `This action returns a #${id} submission`;
  }

  update(id: number, updateSubmissionDto: UpdateSubmissionDto) {
    return `This action updates a #${id} submission`;
  }

  remove(id: number) {
    return `This action removes a #${id} submission`;
  }

  async startAssessment(startQuizDto: StartQuizDto) {
    const submission = new Submission();
    submission.startAt = new Date();
    submission.userId = startQuizDto.userId;

    // use EntityManager instead of Repository
    const assessment = await this.entityManager.findOne(Assessment, {
      where: { linkURL: startQuizDto.linkUrl },
    });

    if (!assessment) {
      throw new Error('Assessment not found');
    }
    submission.assessmentId = assessment.id;
    return this.entityManager.save(Submission, submission);
  }

  async endAssessment(submissionId: number) {
    const submission = await this.entityManager.findOne(Submission, {
      where: { id: submissionId },
    });
    if (!submission) {
      throw new Error('Submission not found');
    }

    submission.endAt = new Date();
    return this.entityManager.save(Submission, submission);
  }
}
