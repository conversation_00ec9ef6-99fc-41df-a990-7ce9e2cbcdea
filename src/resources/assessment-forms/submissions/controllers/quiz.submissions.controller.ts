import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { SubmissionsService } from '../submissions.service';
import { ApiTags } from '@nestjs/swagger';
import { StartQuizDto } from '../../dto/start-quiz.dto';

@ApiTags('Submissions Quiz')
@Controller('submissions/quiz')
export class QuizSubmissionsController {
  constructor(private readonly submissionsService: SubmissionsService) {}

  @Post('start')
  startAssessment(@Body() startQuizDto: StartQuizDto) {
    return this.submissionsService.startAssessment(startQuizDto);
  }

  @Patch('end/:id')
  endAssessment(@Param('id') id: number) {
    return this.submissionsService.endAssessment(id);
  }
}
