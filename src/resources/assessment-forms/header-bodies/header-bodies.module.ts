import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HeaderBodiesService } from './header-bodies.service';
import { HeaderBodiesController } from './header-bodies.controller';
import { HeaderBody } from '../entities/header-body.entity';
import { ItemBlock } from '../entities/item-block.entity';

@Module({
  imports: [TypeOrmModule.forFeature([HeaderBody, ItemBlock])],
  controllers: [HeaderBodiesController],
  providers: [HeaderBodiesService],
  exports: [HeaderBodiesService],
})
export class HeaderBodiesModule {}
