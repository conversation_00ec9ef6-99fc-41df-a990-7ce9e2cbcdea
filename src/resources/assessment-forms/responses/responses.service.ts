import { Injectable, NotFoundException } from '@nestjs/common';

import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { ChartData } from '../dto/chart-data.dto';
import { Assessment } from '../entities/assessment.entity';
import { Response } from '../entities/response.entity';
import type { UpdateResponseDto } from '../dto/updates/update-response.dto';
import type { CreateResponseDto } from '../dto/creates/create-response.dto';
import { Option } from '../entities/option.entity';
import { Question } from '../entities/question.entity';
import { Submission } from '../entities/submission.entity';

@Injectable()
export class ResponsesService {
  constructor(
    @InjectEntityManager()
    private entityManager: EntityManager,
    @InjectRepository(Assessment)
    private assessmentRepo: Repository<Assessment>,
    @InjectRepository(Response)
    private responseRepo: Repository<Response>,
  ) {}
  async getChartData(assessmentId: number): Promise<ChartData[]> {
    const chartDatas: ChartData[] = [];

    const assessment = await this.assessmentRepo
      .createQueryBuilder('a')
      .leftJoinAndSelect('a.itemBlocks', 'ib')
      .leftJoinAndSelect('ib.questions', 'q')
      .leftJoinAndSelect('q.responses', 'r')
      .leftJoinAndSelect('r.selectedOption', 'so')
      .leftJoinAndSelect('ib.options', 'o')
      .where('a.id = :id', { id: assessmentId })
      .andWhere('ib.type NOT IN (:...types)', {
        types: ['UPLOAD', 'IMAGE', 'HEADER'],
      })
      .orderBy('q.sequence', 'ASC')
      .addOrderBy('o.sequence', 'ASC')
      .getOne();

    if (!assessment) {
      throw new NotFoundException(
        `Assessment with ID ${assessmentId} not found`,
      );
    }

    for (const itemBlock of assessment.itemBlocks) {
      const type = ['RADIO', 'CHECKBOX', 'GRID'].includes(itemBlock.type)
        ? 'choice'
        : 'text';

      let title = '';
      if (type === 'text') {
        const textQuestions = itemBlock.questions.filter(
          (q) => q.isHeader === false,
        );
        title = textQuestions.length > 0 ? textQuestions[0].questionText : '';
      } else {
        const titleQuestion = itemBlock.questions.find(
          (q) => q.isHeader === true,
        );
        title = titleQuestion ? titleQuestion.questionText : '';
      }

      if (type === 'choice') {
        const questions = itemBlock.questions
          .filter((q) => q.isHeader === false)
          .sort((a, b) => a.sequence - b.sequence);

        const options = itemBlock.options
          ? itemBlock.options.sort((a, b) => a.sequence - b.sequence)
          : [];

        const labels = questions.map((q) => q.questionText);
        const datasets = options.map((option) => {
          const values = questions.map((question) => {
            const responseCount = question.responses
              ? question.responses.filter(
                  (r) => r.selectedOption && r.selectedOption.id === option.id,
                ).length
              : 0;
            return responseCount;
          });
          return { label: option.optionText, values };
        });

        chartDatas.push({
          section: itemBlock.section,
          sequence: itemBlock.sequence,
          title,
          labels,
          datasets,
          type,
        });
      } else {
        const questions = itemBlock.questions.filter(
          (q) => q.isHeader === false,
        );
        const allResponses = questions.flatMap((q) => q.responses || []);

        const options = itemBlock.options
          ? itemBlock.options.sort((a, b) => a.sequence - b.sequence)
          : [];

        const datasets = options.map((option) => {
          const count = allResponses.filter(
            (r) => r.selectedOption && r.selectedOption.id === option.id,
          ).length;
          return {
            label: option.optionText,
            values: [count],
          };
        });

        if (datasets.length > 0) {
          chartDatas.push({
            section: itemBlock.section,
            sequence: itemBlock.sequence,
            title,
            labels: [], // ไม่มีหลายคำถาม จึงไม่มี label
            datasets,
            type,
          });
        }
      }
    }

    return chartDatas.sort((a, b) => {
      if (a.section !== b.section) {
        return a.section - b.section;
      }
      return a.sequence - b.sequence;
    });
  }

  async getNumberOfResponses(assessmentId: number): Promise<number> {
    const result = await this.assessmentRepo.query(
      `SELECT A.name, COUNT(*) as number
     FROM assessments AS A
     JOIN submissions AS S ON A.id = S.assessmentId
     WHERE A.id = ${assessmentId}`,
    );
    return result[0]; // แปลงผลลัพธ์เป็น number
  }
  async create(createResponseDto: CreateResponseDto): Promise<Response> {
    const response = this.responseRepo.create(createResponseDto);
    return await this.responseRepo.save(response);
  }

  findAll() {
    return `This action returns all responses`;
  }

  findOne(id: number) {
    return `This action returns a #${id} response`;
  }
  async update(
    id: number,
    updateResponseDto: UpdateResponseDto,
  ): Promise<Response> {
    await this.responseRepo.update(id, updateResponseDto);
    return await this.responseRepo.findOneOrFail({
      where: { id },
      relations: ['submission', 'selectedOption', 'question'],
    });
  }

  async remove(id: number): Promise<void> {
    await this.responseRepo.delete(id);
  }

  async userSaveQuizResponse(createResponseDto: CreateResponseDto) {
    //validate user sent response is in time of submission and assessment
    const submission = await this.entityManager.findOne(Submission, {
      where: { id: createResponseDto.submissionId },
    });
    if (!submission) {
      throw new NotFoundException(
        `Submission with ID ${createResponseDto.submissionId} not found`,
      );
    }
    if (submission.endAt < new Date()) {
      throw new Error('Submission is closed');
    }

    if (createResponseDto.answerText) {
      // find question
      const question = await this.entityManager.findOne(Question, {
        where: { id: createResponseDto.questionId },
      });
      if (!question) {
        throw new NotFoundException(
          `Question with ID ${createResponseDto.questionId} not found`,
        );
      }

      const option = this.entityManager.create(Option, {
        itemBlockId: question.itemBlockId,
        optionText: createResponseDto.answerText,
        sequence: 1,
        value: 0,
        nextSection: null,
      });
      await this.entityManager.save(Option, option);
      createResponseDto.selectedOptionId = option.id;
    }

    const response = this.entityManager.create(Response, createResponseDto);
    return this.entityManager.save(Response, response);
  }
}
