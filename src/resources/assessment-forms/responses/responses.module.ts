import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ResponsesService } from './responses.service';
import { ResponsesController } from './responses.controller';
import { Assessment } from '../entities/assessment.entity';
import { Response } from '../entities/response.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Response, Assessment])],
  controllers: [ResponsesController],
  providers: [ResponsesService],
  exports: [ResponsesService],
})
export class ResponsesModule {}
