import { IsDate, IsNotEmpty, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class CreateSubmissionDto {
  @ApiProperty({
    description: 'The date and time when the user started the assessment',
    type: Date,
    example: '2023-01-15T10:30:00Z',
  })
  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  startAt: Date;

  @ApiProperty({
    description: 'The date and time when the user completed the assessment',
    type: Date,
    example: '2023-01-15T11:45:00Z',
  })
  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  endAt: Date;

  @ApiProperty({
    description: 'ID of the user who submitted the assessment',
    type: Number,
  })
  @IsNotEmpty()
  @IsNumber()
  userId: number;

  @ApiProperty({
    description: 'ID of the assessment being submitted',
    type: Number,
  })
  @IsNotEmpty()
  @IsNumber()
  assessmentId: number;
}
