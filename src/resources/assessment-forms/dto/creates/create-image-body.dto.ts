import { IsOptional, IsString, Max<PERSON>ength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateImageBodyDto {
  @ApiProperty({
    description: 'Caption or text associated with the image',
    type: String,
    maxLength: 255,
    required: false,
    example: 'Figure 1: System Architecture Diagram',
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  imageText?: string;

  @ApiProperty({
    description: 'Path to the image file',
    type: String,
    maxLength: 255,
    required: false,
    example: 'uploads/images/diagram.jpg',
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  imagePath?: string;

  @ApiProperty({
    description: 'ID of the item block this image belongs to',
    type: Number,
    required: false,
  })
  @IsOptional()
  itemBlockId?: number;

  @ApiProperty({
    description: 'Width of the image in pixels',
    type: Number,
    required: false,
    example: 800,
  })
  @IsOptional()
  @MaxLength(255)
  imageWidth?: number;

  @ApiProperty({
    description: 'Height of the image in pixels',
    type: Number,
    required: false,
    example: 600,
  })
  @IsOptional()
  @MaxLength(255)
  imageHeight?: number;
}
