import { IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateResponseDto {
  @ApiProperty({
    description:
      'The text answer provided by the user (for text-based questions)',
    type: String,
    required: false,
    example: 'Paris is the capital of France',
  })
  @IsOptional()
  @IsString()
  answerText?: string;

  @ApiProperty({
    description: 'ID of the submission this response belongs to',
    type: Number,
  })
  @IsNotEmpty()
  @IsNumber()
  submissionId: number;

  @ApiProperty({
    description: 'ID of the selected option (for multiple choice questions)',
    type: Number,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  selectedOptionId?: number;

  @ApiProperty({
    description: 'ID of the question this response answers',
    type: Number,
  })
  @IsNotEmpty()
  @IsNumber()
  questionId: number;
}
