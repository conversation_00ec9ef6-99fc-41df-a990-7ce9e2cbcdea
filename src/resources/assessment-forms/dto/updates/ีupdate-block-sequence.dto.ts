import { IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { IsInt, Min } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateItemBlockSequenceDto {
  @ApiProperty({
    description: 'ID of the item block to update',
    type: Number,
  })
  @IsInt()
  id: number;

  @ApiProperty({
    description: 'New sequence number for the item block',
    type: Number,
    minimum: 0,
  })
  @IsInt()
  @Min(0)
  sequence: number;
}

export class UpdateItemBlockSequencesDto {
  @ApiProperty({
    description: 'Array of item blocks with their new sequence numbers',
    type: [UpdateItemBlockSequenceDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateItemBlockSequenceDto)
  itemBlocks: UpdateItemBlockSequenceDto[];
}
