import { PartialType, ApiProperty } from '@nestjs/swagger';
import { CreateOptionDto } from '../creates/create-option.dto';
import { IsNumber, IsOptional } from 'class-validator';

export class UpdateOptionDto extends PartialType(CreateOptionDto) {
  @ApiProperty({
    description: 'ID of the option to update',
    type: Number,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  id?: number;
}
