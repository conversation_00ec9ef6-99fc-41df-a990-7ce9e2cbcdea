import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiConsumes,
} from '@nestjs/swagger';
import { CreateAssessmentDto } from './dto/creates/create-assessment.dto';
import { UpdateAssessmentDto } from './dto/updates/update-assessment.dto';
import { Assessment } from './entities/assessment.entity';
import type { DataParams, DataResponse } from 'src/types/params';
import { AssessmentType } from './enums/assessment-type.enum';
import { AssessmentsService } from './services/assessments.service';
import { AnyFilesInterceptor, FileInterceptor } from '@nestjs/platform-express';

@ApiTags('Assessment')
@Controller('assessments')
export class AssessmentsController {
  constructor(
    private readonly assessmentsService: AssessmentsService,
    // private readonly evaluateService: EvaluateService,
    // private readonly quizService: QuizService,
  ) {}

  @Post()
  @ApiOperation({
    summary: 'New Assessment (Quiz/Evaluate',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: 'object',
    description: 'Create a new assessment with the required fields.',
    schema: {
      type: 'object',
      properties: {
        creatorUserId: { type: 'number', example: 1 },
        type: {
          type: 'string',
          enum: [AssessmentType.QUIZ, AssessmentType.EVALUATE],
          example: AssessmentType.QUIZ,
        },
        programId: { type: 'number', example: 1 },
      },
      required: ['programId'],
    },
  })
  @UseInterceptors(AnyFilesInterceptor())
  create(@Body() dto: CreateAssessmentDto) {
    console.log('Create Assessment DTO:', dto);
    return this.assessmentsService.createOne(dto);
  }

  @Get()
  async getAll(
    @Query() query: DataParams,
    @Query('type') type: AssessmentType,
  ): Promise<DataResponse<Assessment>> {
    return this.assessmentsService.getAll(query, type);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a single assessment by ID' })
  @ApiParam({
    name: 'id',
    description: 'The ID of the assessment',
    type: Number,
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved assessment.',
    type: Assessment,
  })
  @ApiResponse({ status: 404, description: 'Assessment not found.' })
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<Assessment> {
    return this.assessmentsService.findOne(id);
  }

  @Get('/preview/:id')
  getOne(@Param('id') id: number, @Query('section') section: number) {
    return this.assessmentsService.findOneBySection(+id, +section);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an existing assessment' })
  @ApiParam({
    name: 'id',
    example: 1,
  })
  @ApiBody({ type: UpdateAssessmentDto })
  @ApiResponse({
    status: 200,
    description: 'The assessment has been successfully updated.',
    type: Assessment,
  })
  @ApiResponse({ status: 404, description: 'Assessment not found.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateAssessmentDto: UpdateAssessmentDto,
  ): Promise<Assessment> {
    return this.assessmentsService.update(id, updateAssessmentDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.assessmentsService.remove(+id);
  }

  // * Example Dashboard for Quiz
  @Get('dashboard/quiz')
  async getQuizDashboard() {
    // return this.quizService.getDashboard();
  }

  // * Example Dashboard for Evaluate
  @Get('dashboard/evaluate')
  async getEvaluateDashboard() {
    // return this.evaluateService.getDashboard();
  }

  // แบบทดสอบสำหรับ user
  @Get('response/quiz')
  async getAllQuiz(@Query() userId: number) {
    // return this.quizService.getResponseAll(userId);
  }

  // แบบประเมินสำหรับ user
  @Get('response/evaluate')
  async getAllEvaluate(@Query() query: DataParams) {
    // return this.evaluateService.getResponseAll(userId);
  }
}
