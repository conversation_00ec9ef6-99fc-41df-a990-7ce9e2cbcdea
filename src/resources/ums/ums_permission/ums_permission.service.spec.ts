import { Test, TestingModule } from '@nestjs/testing';
import { UmsPermissionService } from './ums_permission.service';

describe('UmsPermissionService', () => {
  let service: UmsPermissionService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [UmsPermissionService],
    }).compile();

    service = module.get<UmsPermissionService>(UmsPermissionService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
