import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import {
  UpdateUserDto,
  UpdateUserPasswordDto,
  UpdateUserRolesDto,
} from './dto/update-user.dto';
import { Role } from 'src/resources/roles/entities/role.entity';
import * as bcrypt from 'bcrypt';

const SALT_ROUNDS = 10; // For bcrypt

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<Omit<User, 'password'>> {
    const { email, password, roleIds, ...userData } = createUserDto;

    const existingUser = await this.userRepository.findOneBy({ email });
    if (existingUser) {
      throw new ConflictException(`User with email "${email}" already exists.`);
    }

    const hashedPassword = await bcrypt.hash(password, SALT_ROUNDS);
    if (!hashedPassword) {
      throw new InternalServerErrorException('Failed to hash password.');
    }

    const user = this.userRepository.create({
      ...userData,
      email,
      password: hashedPassword,
    });

    if (roleIds && roleIds.length > 0) {
      const roles = await this.roleRepository.findBy({ id: In(roleIds) });
      if (roles.length !== roleIds.length) {
        const foundRoleIdsSet = new Set(roles.map((role) => role.id));
        const notFoundIds = roleIds.filter((id) => !foundRoleIdsSet.has(id));
        throw new BadRequestException(
          `One or more roles not found: IDs ${notFoundIds.join(', ')}.`,
        );
      }
      user.roles = roles;
    } else {
      // Optional: Assign a default role if no roleIds are provided
      // const defaultRole = await this.roleRepository.findOneBy({ name: 'student' }); // Example
      // if (defaultRole) user.roles = [defaultRole];
      user.roles = []; // Or ensure it's initialized if not eager loaded sometimes
    }

    const savedUser = await this.userRepository.save(user);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password: _, ...result } = savedUser; // Exclude password from return
    return result;
  }

  async findAll(): Promise<Omit<User, 'password'>[]> {
    const users = await this.userRepository.find({
      relations: ['roles' /* 'quizAttempts' */], // Roles are eager, quizAttempts are not by default
    });
    return users.map((user) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { password, ...result } = user;
      return result;
    });
  }

  async findOne(
    id: number,
    loadRelations: string[] = ['roles'],
  ): Promise<Omit<User, 'password'>> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: loadRelations,
    });
    if (!user) {
      throw new NotFoundException(`User with ID "${id}" not found`);
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...result } = user;
    return result;
  }

  async findOneByEmail(
    email: string,
    loadRelations: string[] = ['roles'],
  ): Promise<User | null> {
    // This method might be used internally for auth, so it returns the full User object including password
    return this.userRepository.findOne({
      where: { email },
      relations: loadRelations,
    });
  }

  async update(
    id: number,
    updateUserDto: UpdateUserDto,
  ): Promise<Omit<User, 'password'>> {
    const user = await this.userRepository.findOneBy({ id });
    if (!user) {
      throw new NotFoundException(`User with ID "${id}" not found.`);
    }

    if (updateUserDto.email && updateUserDto.email !== user.email) {
      const existingUserWithNewEmail = await this.userRepository.findOneBy({
        email: updateUserDto.email,
      });
      if (existingUserWithNewEmail && existingUserWithNewEmail.id !== id) {
        throw new ConflictException(
          `User with email "${updateUserDto.email}" already exists.`,
        );
      }
    }

    this.userRepository.merge(user, updateUserDto);
    const updatedUser = await this.userRepository.save(user);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...result } = updatedUser;
    return result;
  }

  async updateUserPassword(
    id: number,
    updateUserPasswordDto: UpdateUserPasswordDto,
  ): Promise<{ message: string }> {
    const { currentPassword, newPassword } = updateUserPasswordDto;
    const user = await this.userRepository.findOneBy({ id });
    const isOldPasswordMatching = await bcrypt.compare(
      currentPassword,
      user.password,
    );
    if (!isOldPasswordMatching) {
      throw new BadRequestException('Old password does not match.');
    }
    if (newPassword.length < 8) {
      throw new BadRequestException(
        'New password must be at least 8 characters long.',
      );
    }

    user.password = await bcrypt.hash(newPassword, SALT_ROUNDS);
    await this.userRepository.save(user);
    return { message: 'Password updated successfully.' };
  }

  async updateUserRoles(
    id: number,
    updateUserRolesDto: UpdateUserRolesDto,
  ): Promise<Omit<User, 'password'>> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['roles'],
    }); // Load current roles
    if (!user) {
      throw new NotFoundException(`User with ID "${id}" not found.`);
    }

    const { roleIds } = updateUserRolesDto;
    if (roleIds && roleIds.length > 0) {
      const newRoles = await this.roleRepository.findBy({ id: In(roleIds) });
      if (newRoles.length !== roleIds.length) {
        const foundRoleIds = newRoles.map((r) => r.id);
        const notFoundIds = roleIds.filter((id) => !foundRoleIds.includes(id));
        throw new BadRequestException(
          `One or more roles not found: IDs ${notFoundIds.join(', ')}.`,
        );
      }
      user.roles = newRoles;
    } else {
      user.roles = [];
    }

    const updatedUser = await this.userRepository.save(user);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...result } = updatedUser;
    return result;
  }

  async remove(id: number): Promise<void> {
    const user = await this.userRepository.findOneBy({ id });
    if (!user) {
      throw new NotFoundException(`User with ID "${id}" not found.`);
    }
    await this.userRepository.remove(user);
  }
}
