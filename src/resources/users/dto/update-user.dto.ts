import { PartialType } from '@nestjs/mapped-types';
import {
  IsString,
  <PERSON><PERSON><PERSON>al,
  IsEmail,
  <PERSON><PERSON><PERSON><PERSON>,
  MaxLength,
  IsArray,
  IsInt,
  IsNotEmpty,
  Validate,
} from 'class-validator';

/**
 * Base DTO for user profile updates
 * Contains common user properties that can be updated
 */
export class UserProfileBaseDto {
  @IsString({ message: 'Name must be a string' })
  @IsOptional()
  @MaxLength(100, { message: 'Name must not exceed 100 characters' })
  name?: string;

  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsOptional()
  @MaxLength(100, { message: 'Email must not exceed 100 characters' })
  email?: string;
}

/**
 * DTO for general user profile updates
 * Inherits all properties from the base DTO as optional fields
 */
export class UpdateUserDto extends PartialType(UserProfileBaseDto) {}

/**
 * DTO specifically for password updates
 * Kept separate for security and single responsibility principle
 */
export class UpdateUserPasswordDto {
  @IsString({ message: 'Password must be a string' })
  @IsNotEmpty({ message: 'Password is required' })
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  newPassword: string;

  @IsString({ message: 'Current password must be a string' })
  @IsNotEmpty({ message: 'Current password is required for verification' })
  currentPassword: string;
}

/**
 * DTO for assigning roles to a user
 * Kept separate to handle permissions independently
 */
export class UpdateUserRolesDto {
  @IsArray({ message: 'Role IDs must be provided as an array' })
  @IsInt({ each: true, message: 'Each role ID must be an integer' })
  @IsNotEmpty({ message: 'At least one role ID must be provided' })
  roleIds: number[];
}
