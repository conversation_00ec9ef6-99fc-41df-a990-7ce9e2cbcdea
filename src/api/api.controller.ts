import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { ApiService } from './api.service';
import { FileInterceptor } from '@nestjs/platform-express/multer';
import { UploadFileDto } from 'src/types/api/uploadFile';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { v4 as uuidv4 } from 'uuid';

@Controller('api')
export class ApiController {
  constructor(private readonly apiService: ApiService) {}

  @Post('/sendMail')
  sendMail(@Body() body: any) {
    return this.apiService.sendEmail(
      body?.system,
      body?.to,
      body?.subjet,
      body?.message,
    );
  }

  @Post('/uploadFile')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploaded_file',
        filename: (req, file, cb) => {
          const name = uuidv4();
          cb(null, name + extname(file.originalname));
        },
      }),
    }),
  )
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadFileDto: UploadFileDto,
  ) {
    if (!uploadFileDto.fileName) {
      uploadFileDto.fileName = file.originalname; // ใช้ชื่อไฟล์ต้นฉบับถ้าไม่ได้ระบุ fileName
    }
    return this.apiService.uploadFile(file, uploadFileDto);
  }

  @Post('/getFileList')
  getFileList(@Body() body: any) {
    const path = body.path || '/uploaded_files'; // ตั้งค่า default ให้ชัดเจน
    return this.apiService.getFileList(path);
  }

  @Post('/getPublicFile')
  getPublicFile(@Body() body: any) {
    return this.apiService.getPublicFile(body.fileName);
  }

  @Post('/getPublicFiles')
  getPublicFiles(@Body() body: any) {
    return this.apiService.getPublicFiles(body.files);
  }

  @Post('/sendNoti')
  sendNoti(@Body() body: any) {
    return this.apiService.sendNoti(body.message);
  }

  @Delete('/deleteFile')
  deleteFile(@Body() body: any) {
    return this.apiService.deleteFile(body.fileName);
  }

  @Get('/dsPrefix')
  dsPrefix() {
    return this.apiService.dsPrefix();
  }
}
