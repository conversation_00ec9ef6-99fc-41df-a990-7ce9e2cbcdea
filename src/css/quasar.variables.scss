// Quasar SCSS (& Sass) Variables
// --------------------------------------------------
// To customize the look and feel of this app, you can override
// the Sass/SCSS variables found in Quasar's source Sass/SCSS files.

// Check documentation for full list of Quasar variables

// Your own variables (that are declared here) and Quasar's own
// ones will be available out of the box in your .vue/.scss/.sass files

// It's highly recommended to change the default colors
// to match your app's branding.
// Tip: Use the "Theme Builder" on Quasar's documentation website.

$primary: #ffce26;
$secondary: #474747;
$accent: #0f52ba;
$accent-dark: #39303d;
$surface: #bdbdbd;

$surface-selected: #dddddd;
$accent-selected: #f1ebf8;
$surface-field: #f8f8f8;

$surface-primary: #f5f5ff;

$dark: #1d1d1d;
$dark-page: #121212;

$positive: #25ad45;
$negative: #ab2433;
$info: #31ccec;
$warning: #f2c037;

// * quasar scss variables
$generic-border-radius: 12px;
