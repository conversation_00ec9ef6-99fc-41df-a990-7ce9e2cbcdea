import { defineStore } from 'pinia';
import type { Assessment } from 'src/types/models';

export const useAssessmentStore = defineStore('asm', {
  state() {
    return {
      currentAssessment: null as Assessment | null,
    };
  },
  getters: {
    getCurrentAssessment(): Assessment | null {
      return this.currentAssessment;
    },
  },
  actions: {
    setCurrentAssessment(assessment: Assessment | null) {
      this.currentAssessment = assessment;
    },
    clearCurrentAssessment() {
      this.currentAssessment = null;
    },
  },
});
