import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import authService from '../services/authService';
import { jwtDecode, type JwtPayload } from 'jwt-decode';
import router from '../router';
import type { User } from 'src/types/models';
import { useUtilsStore } from './utils';
import type { AxiosError } from 'axios';

export const useAuthStore = defineStore('auth', () => {
  // State
  const loginUsername = ref('');
  const loginPassword = ref('');
  const incorrectUsernamePasswordStatus = ref(false);
  const notifyDialog = ref(false);
  const notifyMessage = ref('');
  const utilsStore = useUtilsStore();
  const secretKey = 'E13qDGn!Z|"38y/BUvFjl$cA-vs4)P';

  // Getters
  const isLoggedIn = computed(() => {
    return Boolean(localStorage.getItem('access_token'));
  });

  // Actions
  function getCurrentUser(): User | undefined {
    const token = localStorage.getItem('access_token');
    if (!token) return undefined;
    const user = getUserFromToken(token);
    return user;
  }

  function getUserFromToken(token: string): User {
    try {
      const decodedToken = jwtDecode<JwtPayload>(token);
      const decrypted = utilsStore.decryptObject(secretKey, decodedToken.sub!);
      if (!decrypted) throw new Error('Failed to decrypt user object');

      const user = decrypted as unknown as User;

      // 🔒 เก็บ permission (จาก role.permissions) เป็น string เข้ารหัสใน localStorage
      if (user.roles?.[0]?.permissions) {
        const perms =
          utilsStore.encryptString(
            secretKey,
            JSON.stringify(user.roles?.[0]?.permissions),
            false,
          ) || '';
        localStorage.setItem('perms', perms);
      }

      return user;
    } catch (e) {
      console.error('Token decode or decrypt failed:', e);
      return {
        id: 0,
        roleId: 0,
        name: '',
        email: '',
        password: '',
        roles: [
          {
            id: 0,
            userId: 0,
            name: 'Guest', // ✅ ต้องเป็นค่าที่อยู่ใน RoleEnum
            permissions: [],
          },
        ],
      };
    }
  }

  function logout() {
    localStorage.removeItem('access_token');
    localStorage.removeItem('perms');
    localStorage.removeItem('hasVisited');
    void router.push('/');
  }

  function showNotifyDialog(message: string) {
    notifyMessage.value = message;
    notifyDialog.value = true;
  }

  async function loginBuu() {
    utilsStore.isLoading = true;
    incorrectUsernamePasswordStatus.value = false;
    try {
      const encryptedUsername = utilsStore.encryptString(
        'loginBuu_username',
        loginUsername.value,
        false,
      );
      const encryptedPassword = utilsStore.encryptString(
        'loginBuu_password',
        loginPassword.value,
        false,
      );
      localStorage.removeItem('access_token');
      localStorage.removeItem('perms');
      const response = await authService.loginBuu(encryptedUsername!, encryptedPassword!);
      localStorage.setItem('access_token', response.data.access_token);
      // Log after setting token
      await router.replace({ name: 'home' });
      loginUsername.value = '';
      loginPassword.value = '';
    } catch (error) {
      const axiosError = error as AxiosError<{ message: string }>;
      if (axiosError.response?.data.message === 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง') {
        incorrectUsernamePasswordStatus.value = true;
      } else {
        console.error('Login error:', axiosError);
        showNotifyDialog('เกิดข้อผิดพลาดในการเข้าสู่ระบบ');
      }
    } finally {
      utilsStore.isLoading = false;
    }
  }

  return {
    // State
    loginUsername,
    loginPassword,
    incorrectUsernamePasswordStatus,
    notifyDialog,
    notifyMessage,
    // Getters
    isLoggedIn,
    // Actions
    loginBuu,
    getCurrentUser,
    getUserFromToken,
    logout,
    showNotifyDialog,
  };
});
