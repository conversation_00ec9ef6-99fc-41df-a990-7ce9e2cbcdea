import systemPermService from 'src/services/ums/systemPermService';
import type SystemPerm from 'src/types/ums/systemPerm';
import { defineStore } from 'pinia';
import { useAuthStore } from '../auth';
import router from 'src/router';
import { computed } from 'vue';
import { useUtilsStore } from '../utils';
import type { QTableProps } from 'quasar';

export const useSystemPermStore = defineStore('systemPerm', () => {
  const authStore = useAuthStore();
  const utilsStore = useUtilsStore();

  const facId = computed({
    get() {
      return utilsStore.facId;
    },
    set(newValue) {
      utilsStore.facId = newValue;
    },
  });

  const getSystemPermActives = async () => {
    try {
      const response = await systemPermService.getSystemPermActives();
      return response.data;
    } catch (e) {
      console.log(e);
    }
  };

  const getAllSystemPerms = async (
    pagination: QTableProps['pagination'],
    search: { permName: string },
  ) => {
    try {
      const { page, rowsPerPage, sortBy } = pagination ?? {};
      const response = await systemPermService.getAllSystemPerms({
        page: page || 1,
        itemsPerPage: rowsPerPage || 10,
        sortBy: sortBy ?? null,
        search,
      });
      return response.data;
    } catch (e) {
      console.log(e);
    }
  };

  const addSystemPerm = async (data: SystemPerm) => {
    try {
      const user = authStore.getCurrentUser();
      data.updateUser = user?.name || 'sys-permission';
      data.updateProgram = router.currentRoute.value.path;
      await systemPermService.addSystemPerm(data);
    } catch (e) {
      console.log(e);
    }
  };

  const editSystemPerm = async (systemPerm: SystemPerm) => {
    try {
      const user = authStore.getCurrentUser();
      systemPerm.updateUser = user?.name || 'sys-permission';
      systemPerm.updateProgram = router.currentRoute.value.path;
      await systemPermService.editSystemPerm(systemPerm);
    } catch (e) {
      console.log(e);
    }
  };

  const getOneSystemPerm = async (perId: number) => {
    try {
      const response = await systemPermService.getOneSystemPerm(perId);
      return response.data;
    } catch (e) {
      console.log(e);
    }
  };

  const deleteSystemPerm = async (perId: number) => {
    try {
      await systemPermService.deleteSystemPerm(perId);
    } catch (e) {
      console.log(e);
    }
  };

  return {
    getOneSystemPerm,
    getSystemPermActives,
    addSystemPerm,
    editSystemPerm,
    deleteSystemPerm,
    getAllSystemPerms,
    facId,
  };
});
