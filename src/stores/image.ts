import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useImageStore = defineStore('image', () => {
  const imageData = ref('');
  const widthPixel = ref(0);
  const heightPixel = ref(0);

  function setImage(data: string) {
    imageData.value = data;
  }

  function clearImage() {
    imageData.value = '';
  }
  // actions: {
  //   setImage(data: string) {
  //     this.imageData = data
  //   },
  //   clear() {
  //     this.imageData = ''
  //   }
  // }
  return {
    imageData,
    setImage,
    clearImage,
    widthPixel,
    heightPixel,
  };
});
