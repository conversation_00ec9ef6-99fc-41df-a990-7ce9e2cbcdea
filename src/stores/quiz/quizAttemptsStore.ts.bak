import { defineStore } from 'pinia';
import { ref } from 'vue';
import quizAttemptService from 'src/services/quiz/quizAttemptsService';
import type {
  QuizAttempt,
  Question,
  AttemptAnswer,
  Submit,
  NavigateQuestionResponse,
} from 'src/types/quiz/quiz';

export const useQuizAttemptStore = defineStore('quizAttempt', () => {
  const currentAttempt = ref<QuizAttempt | null>(null);
  const currentQuestion = ref<Question | null>(null);
  const currentAttemptAnswer = ref<AttemptAnswer | null>(null);
  const submitResult = ref<Submit | null>(null);
  const loading = ref(false);

  const startAttempt = async (userId: number, quizId: number) => {
    loading.value = true;
    try {
      const { data } = await quizAttemptService.startQuizAttempt(userId, quizId);
      currentAttempt.value = data;
      const navigateToQuestionData = await quizAttemptService.navigateToQuestion(data.id, 1);
      currentQuestion.value = navigateToQuestionData.data.question;
      currentAttemptAnswer.value = navigateToQuestionData.data.attemptAnswer;
      console.log(
        navigateToQuestionData,
        'navigateToQuestionData',
        currentQuestion.value,
        'คำตอบ',
        currentAttemptAnswer.value,
      );
    } finally {
      loading.value = false;
    }
  };

  const loadActiveAttempt = async () => {
    loading.value = true;
    try {
      const { data } = await quizAttemptService.getActiveQuizAttempt();
      currentAttempt.value = data;
    } finally {
      loading.value = false;
    }
  };

  const loadQuestion = async (sequence: number) => {
    if (!currentAttempt.value) return;
    loading.value = true;
    try {
      const { data }: { data: NavigateQuestionResponse } =
        await quizAttemptService.navigateToQuestion(currentAttempt.value.id, sequence);

      currentQuestion.value = data.question;
      currentAttemptAnswer.value = data.attemptAnswer;
    } finally {
      loading.value = false;
    }
  };

  const saveAnswer = async (answer: AttemptAnswer) => {
    if (!currentAttempt.value) return;
    await quizAttemptService.saveAnswer(currentAttempt.value.id, answer);
    currentAttemptAnswer.value = answer;
  };

  const submitAttempt = async () => {
    if (!currentAttempt.value) return;
    const { data } = await quizAttemptService.submitQuizAttempt(currentAttempt.value.id);
    submitResult.value = data;
  };

  const resetStore = () => {
    currentAttempt.value = null;
    currentQuestion.value = null;
    currentAttemptAnswer.value = null;
    submitResult.value = null;
    loading.value = false;
  };

  return {
    currentAttempt,
    currentQuestion,
    currentAttemptAnswer,
    submitResult,
    loading,

    startAttempt,
    loadActiveAttempt,
    loadQuestion,
    saveAnswer,
    submitAttempt,
    resetStore,
  };
});
