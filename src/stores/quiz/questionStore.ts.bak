/* eslint-disable @typescript-eslint/no-explicit-any */
import { defineStore } from 'pinia';
import { ref } from 'vue';
import questionService from 'src/services/quiz/questionService';
import { useQuizStore } from './quizStore';
import type { Question } from 'src/types/models';

// Define QuestionOrder interface locally as it's not exported from questionService.ts
interface QuestionOrder {
  questionId: number;
  sequence: number;
}

export const useQuestionStore = defineStore('question', () => {
  const quizStore = useQuizStore();
  const loading = ref(false);
  const error = ref<string | null>(null);

  const addQuestionToQuiz = async (
    questionData: Partial<Question>,
  ): Promise<Question | undefined> => {
    if (!questionData.quizId) {
      error.value = 'Quiz ID is required to create a question in a quiz.';
      // console.error('Quiz ID is required to create a question in a quiz.');
      return undefined;
    }
    loading.value = true;
    error.value = null;
    try {
      const { data: newQuestion } = await questionService.createQuestion(questionData);
      if (quizStore.currentQuiz && quizStore.currentQuiz.id === newQuestion.quizId) {
        if (!quizStore.currentQuiz.questions) {
          quizStore.currentQuiz.questions = [];
        }
        quizStore.currentQuiz.questions.push(newQuestion);
      }
      // To ensure data consistency, especially with order or server-side changes,
      // consider refetching the quiz or its questions.
      // Example: await quizStore.fetchQuizById(newQuestion.quizId);
      return newQuestion;
    } catch (err: any) {
      error.value = err?.response?.data?.message || err?.message || 'Failed to create question.';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const deleteQuestionFromQuiz = async (quizId: number, questionId: number): Promise<void> => {
    loading.value = true;
    error.value = null;
    try {
      await questionService.deleteQuestion(quizId, questionId);
      if (
        quizStore.currentQuiz &&
        quizStore.currentQuiz.id === quizId &&
        quizStore.currentQuiz.questions
      ) {
        quizStore.currentQuiz.questions = quizStore.currentQuiz.questions.filter(
          (q) => q.id !== questionId,
        );
      }
    } catch (err: any) {
      error.value = err?.response?.data?.message || err?.message || 'Failed to delete question.';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const reorderQuestionsInQuiz = async (
    quizId: number,
    questionOrders: QuestionOrder[],
  ): Promise<Question[] | undefined> => {
    loading.value = true;
    error.value = null;
    try {
      const { data: orderedQuestions } = await questionService.reorderQuestions(
        quizId,
        questionOrders,
      );
      if (quizStore.currentQuiz && quizStore.currentQuiz.id === quizId) {
        quizStore.currentQuiz.questions = orderedQuestions;
      }
      return orderedQuestions;
    } catch (err: any) {
      error.value = err?.response?.data?.message || err?.message || 'Failed to reorder questions.';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updateQuestionDetails = async (
    questionId: number,
    field: string,
    value: unknown,
  ): Promise<Question | undefined> => {
    loading.value = true;
    error.value = null;
    try {
      const { data: updatedQuestion } = await questionService.updateQuestionField(
        questionId,
        field,
        value,
      );
      if (quizStore.currentQuiz && quizStore.currentQuiz.questions) {
        const questionIndex = quizStore.currentQuiz.questions.findIndex((q) => q.id === questionId);
        if (questionIndex !== -1) {
          quizStore.currentQuiz.questions[questionIndex] = {
            ...quizStore.currentQuiz.questions[questionIndex],
            ...updatedQuestion,
          };
        }
      }
      return updatedQuestion;
    } catch (err: any) {
      error.value =
        err?.response?.data?.message || err?.message || 'Failed to update question field.';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const addNewChoiceToQuestion = async (
    questionId: number,
    fieldNameForChoice: string,
    fieldValueForChoice: unknown,
  ): Promise<Choice | undefined> => {
    loading.value = true;
    error.value = null;
    try {
      const { data: newChoice } = await questionService.addChoice(
        questionId,
        fieldNameForChoice,
        fieldValueForChoice,
      );
      if (quizStore.currentQuiz && quizStore.currentQuiz.questions) {
        const question = quizStore.currentQuiz.questions.find((q) => q.id === questionId);
        if (question) {
          if (!question.choices) {
            question.choices = [];
          }
          question.choices.push(newChoice);
        }
      }
      return newChoice;
    } catch (err: any) {
      error.value = err?.response?.data?.message || err?.message || 'Failed to add choice.';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updateChoiceDetails = async (
    questionId: number,
    choiceId: number,
    field: string,
    value: unknown,
  ): Promise<Choice | undefined> => {
    loading.value = true;
    error.value = null;
    try {
      const { data: updatedChoice } = await questionService.updateChoiceField(
        questionId,
        choiceId,
        field,
        value,
      );
      if (quizStore.currentQuiz && quizStore.currentQuiz.questions) {
        const question = quizStore.currentQuiz.questions.find((q) => q.id === questionId);
        if (question && question.choices) {
          const choiceIndex = question.choices.findIndex((c) => c.id === choiceId);
          if (choiceIndex !== -1) {
            question.choices[choiceIndex] = { ...question.choices[choiceIndex], ...updatedChoice };
          }
        }
      }
      return updatedChoice;
    } catch (err: any) {
      error.value =
        err?.response?.data?.message || err?.message || 'Failed to update choice field.';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const removeChoiceFromQuestion = async (questionId: number, choiceId: number): Promise<void> => {
    loading.value = true;
    error.value = null;
    try {
      await questionService.removeChoice(questionId, choiceId);
      if (quizStore.currentQuiz && quizStore.currentQuiz.questions) {
        const question = quizStore.currentQuiz.questions.find((q) => q.id === questionId);
        if (question && question.choices) {
          question.choices = question.choices.filter((c) => c.id !== choiceId);
        }
      }
    } catch (err: any) {
      error.value = err?.response?.data?.message || err?.message || 'Failed to remove choice.';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  return {
    loading,
    error,
    addQuestionToQuiz,
    deleteQuestionFromQuiz,
    reorderQuestionsInQuiz,
    updateQuestionDetails,
    addNewChoiceToQuestion,
    updateChoiceDetails,
    removeChoiceFromQuestion,
  };
});
