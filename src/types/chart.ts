export interface ChartContext {
  chart: {
    data: {
      datasets: Array<{
        data: number[];
      }>;
    };
  };
}

export type ChartType = 'กราฟแท่ง' | 'กราฟวงกลม';

export interface ChartConfig {
  type: 'bar' | 'pie';
  data: {
    labels: string[];
    datasets: Array<{
      label: string;
      data: number[];
      backgroundColor: string[];
      borderColor: string | string[];
      borderWidth: number;
    }>;
  };
  options: {
    responsive: boolean;
    maintainAspectRatio: boolean;
    legend: {
      display: boolean;
      position: 'top' | 'bottom' | 'left' | 'right';
    };
    plugins: {
      datalabels: {
        color: string;
        anchor: 'start' | 'end' | 'center';
        align: 'start' | 'end' | 'center';
        offset: number;
        font: {
          weight: 'normal' | 'bold' | 'bolder';
          size: number;
        };
        formatter: (value: number, context: ChartContext) => string;
      };
    };
    scales: {
      y?: {
        beginAtZero: boolean;
        ticks: {
          stepSize: number;
        };
      };
    };
  };
}

export type ChartData = {
  title: string;
  labels: string[];
  datasets: { label: string; values: number[] }[];
  type: 'choice' | 'text';
};
