import type { DropdownItemBlockType } from './models';

export type System = {
  sysId: number;
  sysUrl: string;
  sysIconSrc: string;
  sysNameEn: string;
  sysNameTh: string;
  perId: number[];
};

export interface MenuLink {
  title: string;
  icon?: string;
  link: string;
  perId?: number[];
}

export interface BlockBodyOptionsType {
  label: string;
  value: DropdownItemBlockType;
  icon: string;
}
