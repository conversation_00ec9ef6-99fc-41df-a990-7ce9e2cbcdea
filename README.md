# intern-front

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Structure

```sh
src/
├── assets/
├── configs/
├── components/
├── layouts/
├── views/
├── router/
├── stores/ (หรือ pinia)
├── composables/ (for composable functions)
├── services/ (API calls)
├── types/
└── App.vue
```

## Commit Message Convention

[Conventional Commits](https://www.conventionalcommits.org):

<type>(optional-scope): <short description>

[optional body]

[optional footer (e.g. closes #123)]

- `feat`: การเพิ่มฟีเจอร์ใหม่
- `fix`: การแก้ไข Bug ต่าง ๆ
- `docs`: การเปลี่ยนแปลง Document
- `style`: การปรับ code style (ไม่กระทบ logic) เช่น white-space, formatting หรือ จนถึงการลืมใส่ semi-colins
- `refactor`: การทำความสะอาดโค้ดให้อ่านง่าย เข้าใจมากยิ่งขึ้น ปรับปรุงโครงสร้างภายใน โดยไม่ได้ทำการแก้ Bug หรือ เพิ่มฟีเจอร์ต่าง ๆ ลงไป
- `chore`: งานเบ็ดเตล็ด เช่น config, การปรับ .gitignore หรือ .prettierrc file
- `perf`: การปรับปรุงประสิทธิภาพการทำงานระบบ
- `build`: การเปลี่ยนแปลงที่ส่งผลกับระบบ build เช่น คำสั่ง npm, การเพิ่ม External Dependencies, การปรับ Dockerfile
- `test`: การเปลี่ยนแปลงในส่วนของ Test ที่เราสร้างไว้ เช่น เพิ่ม Test-case

## Naming Conventions

- ใช้ camelCase สำหรับตัวแปร/function
- PascalCase สำหรับ component/class
- ใช้ data-\* attributes ตามมาตรฐาน HTML ในที่นี้ใช้เป็น data-cy ในการกำหนด locator สำหรับ UI Test

## Project Setup

```sh
$ npm install
# note install more
npm install axios
npm i -D vuetify vite-plugin-vuetify
npm install @mdi/js -D
npm i @date-io/date-fns
npm i crypto-js
npm i @types/crypto-js
npm i jwt-decode
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```

### Run Unit Tests with [Vitest](https://vitest.dev/)

```sh
npm run test:unit
```

### Run End-to-End Tests with [Cypress](https://www.cypress.io/)

```sh
npm run test:e2e:dev
```

This runs the end-to-end tests against the Vite development server.
It is much faster than the production build.

But it's still recommended to test the production build with `test:e2e` before deploying (e.g. in CI environments):

```sh
npm run build
npm run test:e2e
```

### Lint with [ESLint](https://eslint.org/)

```sh
npm run lint
```
